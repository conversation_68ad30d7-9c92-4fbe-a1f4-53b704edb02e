
import datetime
import logging
import urllib3
import tempfile
import json
import time
import threading
import re
import sys
import numpy as np
from scipy.io.wavfile import write,read
from singlecodec_dec import decode
from flask import Flask, request, jsonify
from dspgan.mel2lf0.gen_pitch2 import pitch_synthesis
from dspgan.nhv.resyn2 import generate_audio
import os


ims_app = Flask(__name__)
out_mel_path = 0
@ims_app.route('/api/v1/encmsg', methods=['POST'])
def receive_asr_result():
    # 接收application/x-www-form-urlencoded数据
    data = request.json
    call_id = data.get('id')
    text = data.get('text')
    text_s = data.get('text_s')
    size = data.get('size')
    timestamp = data.get('timestamp')
    try:
        text_np = np.array(text) 
        text_s_np = np.array(text_s) 
    except Exception as e:
        print(f"Error converting lists to NumPy arrays: {e}")
        text_np = text_list # 转换失败，仍然保留 list 格式，或者根据需要处理
        text_s_np = text_s_list # 转换失败，仍然保留 list 格式，或者根据需要处理
    print(f"Received text: {text_np}")
    print(f"Received text_s: {text_s_np}")
    # 打印接收到的数据以便测试
    print("Received data:")
    print("ID:", call_id)
    print("Text:", text)
    print("Timestamp:", timestamp)

    
    # 返回响应
    response_data = {
        "code": 0,
        "data": {
            "id": call_id,
            "text": text,
            "timestamp": timestamp,
        },
        "msg": "enc result received"
    }
    #新线程传递text,text_s,size给audio_io函数
    threading.Thread(target=audio_io, args=(text_np,text_s_np,size)).start()

    
    return jsonify(response_data), 200

def pitch_resyn(mel_path,use_cuda=True):
    return pitch_synthesis(mel_path=mel_path,use_cuda=use_cuda)

def audio_resyn(mel_path, pitch_path,use_cuda=True):
    return generate_audio(
        mel_path=mel_path,
        pitch_path=pitch_path,
        use_cuda=use_cuda
        )


def audio_codec(text_np,text_s_np,size,use_cuda):
    mel = decode(out_mel_path, text_np, text_s_np)
    pitch = pitch_resyn(mel,use_cuda)
    audio = audio_resyn(mel, pitch,use_cuda)
    # print('audio codec done')
    return  audio


def audio_io(text_np,text_s_np,size):
    out_path = 'output.wav'
    use_cuda = True
    s = time.time()
    a = audio_codec(text_np,text_s_np,size,use_cuda)
    e = time.time()
    print(f' codec time: {e-s:.3f}s',' bit:',size)    
    write(out_path,48000,a)



if __name__ == '__main__':
    ims_app.run(port=39004)  
