import torch
import sys
import librosa
import numpy as np
import math  

from extractors import process_wav, __extract_mel
from config import preConfiged16K

global_sm = None
export_path = 'refvq_v1_310000_export.pt'

def decode(out_mel_path, codes_in, codes_s_in, device='cpu'):
    global global_sm
    if global_sm is None:

        global_sm = torch.jit.load(export_path, map_location=torch.device('cpu'))
        global_sm = global_sm.to('cpu') 
        global_sm.eval()

    codes = torch.from_numpy(codes_in).to('cpu') 
    codes_s = torch.from_numpy(codes_s_in).to('cpu')

    assert codes.device == torch.device('cpu'), "输入数据必须位于CPU"
    
    with torch.no_grad():
        pred_mel_encdec = global_sm.decode(codes.cpu(), codes_s.cpu())
        

    pred_mel_encdec = pred_mel_encdec.cpu()  
    pred_mel_encdec = pred_mel_encdec.transpose(1, 2)
    pred_mel_encdec = pred_mel_encdec * 2 - 1
    pred_mel_encdec = torch.transpose(pred_mel_encdec, 1, 2)

    pred_mel_encdec = pred_mel_encdec.squeeze().contiguous().numpy()

    return pred_mel_encdec

