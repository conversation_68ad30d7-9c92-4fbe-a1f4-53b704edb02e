import os
import datetime
import logging
import urllib3
import tempfile
import json
import time
import threading
import re
import sys
import numpy as np
from flask import Flask, request, jsonify
from singlecodec_dec import decode
from singlecodec_enc import encode
import threading

out_mel_path = 0

app = Flask(__name__)

http = urllib3.PoolManager()


    
def async_process_and_send(temp_audio_path, call_id):# 新线程传递
    try:
        codes, codes_s, size = encode(temp_audio_path)
        print('size:',size)
    except Exception as e:
        error_message = "解析出错,请重试。"
        logging.error(f"{error_message}: {str(e)}")
        send_result_to_app(error_message, call_id)

    # time.sleep(0.1)
    send_result_to_app(codes, codes_s, size,call_id)
    os.unlink(temp_audio_path)

@app.route('/api/v1/receive_audio', methods=['POST'])
def receive_audio():
    data = request.form.to_dict()
    call_id = data.get('id')

    print("Received data:")
    print("ID:", call_id)

    if 'audio_file' not in request.files or not call_id:
        return jsonify({"error": "No audio file or call ID provided"}), 400

    audio_file = request.files['audio_file']

    try:
        storage_path = 'temp_audio/'
        if not os.path.exists(storage_path):
            os.makedirs(storage_path)
        _, file_extension = os.path.splitext(audio_file.filename)
        audio_filename = f"{call_id}{file_extension}"
        audio_filepath = os.path.join(storage_path, audio_filename)
        audio_file.save(audio_filepath)
        if os.path.getsize(audio_filepath) < 100:
            os.unlink(audio_filepath)
            return jsonify({"error": "Audio file is too small or empty"}), 400

    except Exception as e:
        return jsonify({"error": f"Failed to process audio file: {str(e)}"}), 500

    responsemsg = jsonify({"message": "Audio received"}), 200

    # 异步处理音频
    threading.Thread(target=async_process_and_send, args=(audio_filepath, call_id)).start()

    return responsemsg


def send_result_to_app(enc_result, enc_result_s, size, call_id):
    # app_url = "http://127.0.0.1:39004/api/v1/encmsg"
    app_url = "http://127.0.0.1:39005/api/v1/receive_codes"
    payload = {
        "text": enc_result.tolist(),
        "text_s": enc_result_s.tolist(),
        "size": size,
        "id": call_id,
        "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        encoded_data = json.dumps(payload).encode('utf-8')
        response = http.request('POST', app_url, 
                                body=encoded_data, 
                                headers={'Content-Type': 'application/json'})
        
        if response.status != 200:
            raise urllib3.exceptions.HTTPError(f"HTTP error {response.status}") 
        logging.info(f"Result sent to IMS platform. Response: {response.data.decode('utf-8')}")

    except urllib3.exceptions.HTTPError as e:
        logging.error(f"Failed to send result to IMS platform: {e}")
        print(f"HTTP Error: {e}")
    except Exception as e:
        logging.error(f"Unexpected error when sending result to IMS platform: {e}")
        print(f"Unexpected error: {e}")



if __name__ == '__main__':
    app.run(host='127.0.0.1', port=38004)
