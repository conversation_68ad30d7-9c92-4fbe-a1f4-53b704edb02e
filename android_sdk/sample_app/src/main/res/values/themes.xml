<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.AudioCodecSample" parent="Theme.Material3.DayNight">
        <!-- Customize your light theme here. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        
        <!-- 状态栏 -->
        <item name="android:statusBarColor">@color/primary_variant</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
        
        <!-- 导航栏 -->
        <item name="android:navigationBarColor">@color/primary_variant</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o">false</item>
    </style>
</resources>
