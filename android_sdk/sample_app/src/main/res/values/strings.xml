<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">AudioCodec SDK 示例</string>
    
    <!-- 主界面 -->
    <string name="status_ready">准备就绪</string>
    <string name="init_sdk">初始化 SDK</string>
    <string name="codec_test">编解码测试</string>
    <string name="performance_test">性能测试</string>
    <string name="sdk_info">SDK 信息</string>
    <string name="release_sdk">释放 SDK</string>
    <string name="version_info">AudioCodec SDK v1.0.0\n基于深度学习的音频编解码</string>
    
    <!-- 编解码测试界面 -->
    <string name="codec_test_title">编解码测试</string>
    <string name="back">返回</string>
    <string name="select_audio_file">选择音频文件</string>
    <string name="select_file">选择文件</string>
    <string name="no_file_selected">未选择文件</string>
    <string name="operations">操作</string>
    <string name="play_original">播放原始音频</string>
    <string name="encode_audio">编码音频</string>
    <string name="decode_audio">解码音频</string>
    <string name="play_decoded">播放解码音频</string>
    <string name="clear_results">清除结果</string>
    
    <!-- 通用 -->
    <string name="ok">确定</string>
    <string name="cancel">取消</string>
    <string name="error">错误</string>
    <string name="success">成功</string>
    <string name="loading">加载中...</string>
    <string name="processing">处理中...</string>
    
    <!-- 权限相关 -->
    <string name="permission_audio_title">音频权限</string>
    <string name="permission_audio_message">应用需要音频权限来录制和处理音频文件</string>
    <string name="permission_storage_title">存储权限</string>
    <string name="permission_storage_message">应用需要存储权限来读取和保存音频文件</string>
    
    <!-- 错误信息 -->
    <string name="error_sdk_not_initialized">SDK 未初始化</string>
    <string name="error_file_not_found">文件未找到</string>
    <string name="error_invalid_file">无效的文件格式</string>
    <string name="error_encoding_failed">编码失败</string>
    <string name="error_decoding_failed">解码失败</string>
    <string name="error_playback_failed">播放失败</string>
    
    <!-- 成功信息 -->
    <string name="success_sdk_initialized">SDK 初始化成功</string>
    <string name="success_encoding_completed">编码完成</string>
    <string name="success_decoding_completed">解码完成</string>
    <string name="success_file_saved">文件保存成功</string>
</resources>
