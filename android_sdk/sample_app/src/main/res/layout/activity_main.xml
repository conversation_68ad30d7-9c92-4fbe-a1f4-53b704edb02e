<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".MainActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 状态显示 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/status_ready"
            android:textSize="16sp"
            android:padding="12dp"
            android:background="@drawable/bg_status"
            android:layout_marginBottom="24dp"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 初始化SDK按钮 -->
        <Button
            android:id="@+id/btn_init_sdk"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/init_sdk"
            android:textSize="16sp"
            android:layout_marginBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/tv_status"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 编解码测试按钮 -->
        <Button
            android:id="@+id/btn_codec_test"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/codec_test"
            android:textSize="16sp"
            android:layout_marginBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/btn_init_sdk"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 性能测试按钮 -->
        <Button
            android:id="@+id/btn_performance_test"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/performance_test"
            android:textSize="16sp"
            android:layout_marginBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/btn_codec_test"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- SDK信息按钮 -->
        <Button
            android:id="@+id/btn_sdk_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/sdk_info"
            android:textSize="16sp"
            android:layout_marginBottom="12dp"
            app:layout_constraintTop_toBottomOf="@id/btn_performance_test"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 释放SDK按钮 -->
        <Button
            android:id="@+id/btn_release_sdk"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/release_sdk"
            android:textSize="16sp"
            android:backgroundTint="@color/button_danger"
            app:layout_constraintTop_toBottomOf="@id/btn_sdk_info"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 版本信息 -->
        <TextView
            android:id="@+id/tv_version"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/version_info"
            android:textSize="12sp"
            android:gravity="center"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/btn_release_sdk"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
