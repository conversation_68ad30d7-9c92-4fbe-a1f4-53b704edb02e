<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".CodecTestActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 返回按钮 -->
        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/back"
            android:textSize="14sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/codec_test_title"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintTop_toTopOf="@id/btn_back"
            app:layout_constraintBottom_toBottomOf="@id/btn_back"
            app:layout_constraintStart_toEndOf="@id/btn_back"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 文件选择区域 -->
        <TextView
            android:id="@+id/tv_file_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/select_audio_file"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/btn_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <Button
            android:id="@+id/btn_select_audio"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/select_file"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_file_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <TextView
            android:id="@+id/tv_selected_file"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/no_file_selected"
            android:textSize="14sp"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/btn_select_audio"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 操作按钮区域 -->
        <TextView
            android:id="@+id/tv_operations_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/operations"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="24dp"
            app:layout_constraintTop_toBottomOf="@id/tv_selected_file"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <LinearLayout
            android:id="@+id/layout_buttons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_operations_label"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <Button
                android:id="@+id/btn_play_original"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/play_original"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/btn_encode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/encode_audio"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/btn_decode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/decode_audio"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/btn_play_decoded"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/play_decoded"
                android:layout_marginBottom="8dp" />

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/clear_results"
                android:backgroundTint="@color/button_danger" />

        </LinearLayout>

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/layout_buttons"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 状态显示 -->
        <TextView
            android:id="@+id/tv_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/status_ready"
            android:textSize="14sp"
            android:padding="12dp"
            android:background="@drawable/bg_status"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/progress_bar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 编码结果 -->
        <TextView
            android:id="@+id/tv_encode_result"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:padding="8dp"
            android:background="@drawable/bg_result"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@id/tv_status"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 解码结果 -->
        <TextView
            android:id="@+id/tv_decode_result"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:padding="8dp"
            android:background="@drawable/bg_result"
            android:layout_marginTop="8dp"
            app:layout_constraintTop_toBottomOf="@id/tv_encode_result"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
