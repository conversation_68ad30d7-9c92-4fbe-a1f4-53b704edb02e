package com.audiocodec.sample

import android.Manifest
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.audiocodec.AudioCodecSDK
import com.audiocodec.model.CodecConfig
import com.audiocodec.sample.databinding.ActivityMainBinding
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import kotlinx.coroutines.launch
import timber.log.Timber

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val audioCodecSDK = AudioCodecSDK.getInstance()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        requestPermissions()
    }
    
    private fun setupUI() {
        binding.apply {
            // 初始化SDK按钮
            btnInitSdk.setOnClickListener {
                initializeSDK()
            }
            
            // 编解码测试按钮
            btnCodecTest.setOnClickListener {
                startActivity(Intent(this@MainActivity, CodecTestActivity::class.java))
            }
            
            // 性能测试按钮
            btnPerformanceTest.setOnClickListener {
                performanceTest()
            }
            
            // 查看SDK信息按钮
            btnSdkInfo.setOnClickListener {
                showSDKInfo()
            }
            
            // 释放SDK按钮
            btnReleaseSdk.setOnClickListener {
                releaseSDK()
            }
        }
        
        updateUIState()
    }
    
    private fun requestPermissions() {
        Dexter.withContext(this)
            .withPermissions(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
            .withListener(object : MultiplePermissionsListener {
                override fun onPermissionsChecked(report: MultiplePermissionsReport) {
                    if (report.areAllPermissionsGranted()) {
                        Timber.d("All permissions granted")
                        binding.tvStatus.text = "权限已获取，可以开始使用"
                    } else {
                        Timber.w("Some permissions denied")
                        binding.tvStatus.text = "需要权限才能正常使用"
                        Toast.makeText(this@MainActivity, "需要权限才能正常使用", Toast.LENGTH_LONG).show()
                    }
                }
                
                override fun onPermissionRationaleShouldBeShown(
                    permissions: List<PermissionRequest>,
                    token: PermissionToken
                ) {
                    token.continuePermissionRequest()
                }
            })
            .check()
    }
    
    private fun initializeSDK() {
        binding.btnInitSdk.isEnabled = false
        binding.tvStatus.text = "正在初始化SDK..."
        
        lifecycleScope.launch {
            try {
                val config = CodecConfig.create16kConfig()
                val success = audioCodecSDK.initialize(this@MainActivity, config)
                
                if (success) {
                    binding.tvStatus.text = "SDK初始化成功"
                    Toast.makeText(this@MainActivity, "SDK初始化成功", Toast.LENGTH_SHORT).show()
                    Timber.i("AudioCodec SDK initialized successfully")
                } else {
                    binding.tvStatus.text = "SDK初始化失败"
                    Toast.makeText(this@MainActivity, "SDK初始化失败", Toast.LENGTH_SHORT).show()
                    Timber.e("Failed to initialize AudioCodec SDK")
                }
            } catch (e: Exception) {
                binding.tvStatus.text = "SDK初始化异常: ${e.message}"
                Toast.makeText(this@MainActivity, "初始化异常: ${e.message}", Toast.LENGTH_LONG).show()
                Timber.e(e, "Exception during SDK initialization")
            } finally {
                updateUIState()
            }
        }
    }
    
    private fun performanceTest() {
        binding.tvStatus.text = "正在进行性能测试..."
        
        lifecycleScope.launch {
            try {
                // 这里可以添加性能测试逻辑
                // 比如测试编解码速度、内存使用等
                
                val startTime = System.currentTimeMillis()
                
                // 模拟性能测试
                kotlinx.coroutines.delay(2000)
                
                val endTime = System.currentTimeMillis()
                val duration = endTime - startTime
                
                binding.tvStatus.text = "性能测试完成，耗时: ${duration}ms"
                Toast.makeText(this@MainActivity, "性能测试完成", Toast.LENGTH_SHORT).show()
                
            } catch (e: Exception) {
                binding.tvStatus.text = "性能测试失败: ${e.message}"
                Toast.makeText(this@MainActivity, "性能测试失败", Toast.LENGTH_SHORT).show()
                Timber.e(e, "Performance test failed")
            }
        }
    }
    
    private fun showSDKInfo() {
        try {
            // 这里可以显示SDK的详细信息
            val info = """
                AudioCodec SDK 信息:
                版本: 1.0.0
                支持格式: WAV, MP3, M4A, AAC
                最小API级别: 21
                目标API级别: 34
                架构支持: ARM64, ARMv7
            """.trimIndent()
            
            binding.tvStatus.text = info
            
        } catch (e: Exception) {
            binding.tvStatus.text = "获取SDK信息失败: ${e.message}"
            Timber.e(e, "Failed to get SDK info")
        }
    }
    
    private fun releaseSDK() {
        try {
            audioCodecSDK.release()
            binding.tvStatus.text = "SDK已释放"
            Toast.makeText(this, "SDK已释放", Toast.LENGTH_SHORT).show()
            Timber.i("AudioCodec SDK released")
        } catch (e: Exception) {
            binding.tvStatus.text = "释放SDK失败: ${e.message}"
            Toast.makeText(this, "释放SDK失败", Toast.LENGTH_SHORT).show()
            Timber.e(e, "Failed to release SDK")
        } finally {
            updateUIState()
        }
    }
    
    private fun updateUIState() {
        // 根据SDK状态更新UI
        binding.apply {
            btnInitSdk.isEnabled = true
            btnCodecTest.isEnabled = true
            btnPerformanceTest.isEnabled = true
            btnSdkInfo.isEnabled = true
            btnReleaseSdk.isEnabled = true
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 确保释放资源
        try {
            audioCodecSDK.release()
        } catch (e: Exception) {
            Timber.e(e, "Error releasing SDK in onDestroy")
        }
    }
}
