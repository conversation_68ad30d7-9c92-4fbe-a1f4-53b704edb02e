package com.audiocodec.sample

import android.content.Intent
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.audiocodec.AudioCodecSDK
import com.audiocodec.callback.SimpleCodecCallback
import com.audiocodec.model.CodecResult
import com.audiocodec.sample.databinding.ActivityCodecTestBinding
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream

class CodecTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityCodecTestBinding
    private val audioCodecSDK = AudioCodecSDK.getInstance()
    private var selectedAudioFile: String? = null
    private var encodedCodes: ByteArray? = null
    private var encodedCodesS: ByteArray? = null
    private var mediaPlayer: MediaPlayer? = null
    
    // 文件选择器
    private val audioPickerLauncher = registerForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri ->
        uri?.let { handleSelectedAudio(it) }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCodecTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        updateUIState()
    }
    
    private fun setupUI() {
        binding.apply {
            // 返回按钮
            btnBack.setOnClickListener {
                finish()
            }
            
            // 选择音频文件
            btnSelectAudio.setOnClickListener {
                selectAudioFile()
            }
            
            // 播放原始音频
            btnPlayOriginal.setOnClickListener {
                playOriginalAudio()
            }
            
            // 编码音频
            btnEncode.setOnClickListener {
                encodeAudio()
            }
            
            // 解码音频
            btnDecode.setOnClickListener {
                decodeAudio()
            }
            
            // 播放解码音频
            btnPlayDecoded.setOnClickListener {
                playDecodedAudio()
            }
            
            // 清除结果
            btnClear.setOnClickListener {
                clearResults()
            }
        }
    }
    
    private fun selectAudioFile() {
        try {
            audioPickerLauncher.launch("audio/*")
        } catch (e: Exception) {
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()
            Timber.e(e, "Failed to open audio picker")
        }
    }
    
    private fun handleSelectedAudio(uri: Uri) {
        try {
            // 将选中的音频文件复制到应用内部存储
            val inputStream = contentResolver.openInputStream(uri)
            val fileName = "selected_audio_${System.currentTimeMillis()}.wav"
            val outputFile = File(filesDir, fileName)
            
            inputStream?.use { input ->
                FileOutputStream(outputFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            selectedAudioFile = outputFile.absolutePath
            binding.tvSelectedFile.text = "已选择: $fileName"
            binding.tvStatus.text = "音频文件已选择，可以开始编码"
            
            updateUIState()
            
        } catch (e: Exception) {
            Toast.makeText(this, "处理音频文件失败: ${e.message}", Toast.LENGTH_LONG).show()
            Timber.e(e, "Failed to handle selected audio")
        }
    }
    
    private fun playOriginalAudio() {
        selectedAudioFile?.let { filePath ->
            try {
                stopMediaPlayer()
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(filePath)
                    prepare()
                    start()
                    setOnCompletionListener {
                        binding.tvStatus.text = "原始音频播放完成"
                    }
                }
                binding.tvStatus.text = "正在播放原始音频..."
            } catch (e: Exception) {
                Toast.makeText(this, "播放失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Timber.e(e, "Failed to play original audio")
            }
        }
    }
    
    private fun encodeAudio() {
        selectedAudioFile?.let { filePath ->
            binding.btnEncode.isEnabled = false
            binding.tvStatus.text = "正在编码音频..."
            binding.progressBar.visibility = android.view.View.VISIBLE
            
            val startTime = System.currentTimeMillis()
            
            audioCodecSDK.encodeAudio(filePath, object : SimpleCodecCallback(
                onSuccessCallback = { result ->
                    val endTime = System.currentTimeMillis()
                    val duration = endTime - startTime
                    
                    encodedCodes = result.codes
                    encodedCodesS = result.codesS
                    
                    binding.apply {
                        tvStatus.text = "编码完成"
                        tvEncodeResult.text = """
                            编码结果:
                            压缩大小: ${result.compressedSize} bits
                            处理时间: ${duration} ms
                            音频时长: ${"%.2f".format(result.audioDurationSec)} 秒
                            压缩比: ${"%.2f".format(result.compressedSize.toFloat() / (result.audioDurationSec * 16000 * 16))}
                        """.trimIndent()
                        
                        progressBar.visibility = android.view.View.GONE
                        btnEncode.isEnabled = true
                    }
                    
                    updateUIState()
                    Toast.makeText(this@CodecTestActivity, "编码成功", Toast.LENGTH_SHORT).show()
                },
                onErrorCallback = { error ->
                    binding.apply {
                        tvStatus.text = "编码失败: $error"
                        tvEncodeResult.text = "编码失败"
                        progressBar.visibility = android.view.View.GONE
                        btnEncode.isEnabled = true
                    }
                    Toast.makeText(this@CodecTestActivity, "编码失败: $error", Toast.LENGTH_LONG).show()
                },
                onProgressCallback = { progress ->
                    binding.tvStatus.text = "编码进度: $progress%"
                }
            ))
        }
    }
    
    private fun decodeAudio() {
        if (encodedCodes != null && encodedCodesS != null) {
            binding.btnDecode.isEnabled = false
            binding.tvStatus.text = "正在解码音频..."
            binding.progressBar.visibility = android.view.View.VISIBLE
            
            val outputFileName = "decoded_audio_${System.currentTimeMillis()}.wav"
            val outputPath = File(filesDir, outputFileName).absolutePath
            val startTime = System.currentTimeMillis()
            
            audioCodecSDK.decodeAudio(encodedCodes!!, encodedCodesS!!, outputPath, 
                object : SimpleCodecCallback(
                    onSuccessCallback = { result ->
                        val endTime = System.currentTimeMillis()
                        val duration = endTime - startTime
                        
                        binding.apply {
                            tvStatus.text = "解码完成"
                            tvDecodeResult.text = """
                                解码结果:
                                输出文件: $outputFileName
                                处理时间: ${duration} ms
                                音频时长: ${"%.2f".format(result.audioDurationSec)} 秒
                            """.trimIndent()
                            
                            progressBar.visibility = android.view.View.GONE
                            btnDecode.isEnabled = true
                        }
                        
                        updateUIState()
                        Toast.makeText(this@CodecTestActivity, "解码成功", Toast.LENGTH_SHORT).show()
                    },
                    onErrorCallback = { error ->
                        binding.apply {
                            tvStatus.text = "解码失败: $error"
                            tvDecodeResult.text = "解码失败"
                            progressBar.visibility = android.view.View.GONE
                            btnDecode.isEnabled = true
                        }
                        Toast.makeText(this@CodecTestActivity, "解码失败: $error", Toast.LENGTH_LONG).show()
                    },
                    onProgressCallback = { progress ->
                        binding.tvStatus.text = "解码进度: $progress%"
                    }
                ))
        }
    }
    
    private fun playDecodedAudio() {
        val decodedFiles = filesDir.listFiles { file ->
            file.name.startsWith("decoded_audio_") && file.name.endsWith(".wav")
        }
        
        val latestFile = decodedFiles?.maxByOrNull { it.lastModified() }
        
        latestFile?.let { file ->
            try {
                stopMediaPlayer()
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(file.absolutePath)
                    prepare()
                    start()
                    setOnCompletionListener {
                        binding.tvStatus.text = "解码音频播放完成"
                    }
                }
                binding.tvStatus.text = "正在播放解码音频..."
            } catch (e: Exception) {
                Toast.makeText(this, "播放失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Timber.e(e, "Failed to play decoded audio")
            }
        } ?: run {
            Toast.makeText(this, "没有找到解码音频文件", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun clearResults() {
        stopMediaPlayer()
        selectedAudioFile = null
        encodedCodes = null
        encodedCodesS = null
        
        binding.apply {
            tvSelectedFile.text = "未选择文件"
            tvStatus.text = "请选择音频文件开始测试"
            tvEncodeResult.text = ""
            tvDecodeResult.text = ""
        }
        
        // 清理临时文件
        lifecycleScope.launch {
            try {
                filesDir.listFiles { file ->
                    file.name.startsWith("selected_audio_") || file.name.startsWith("decoded_audio_")
                }?.forEach { it.delete() }
            } catch (e: Exception) {
                Timber.e(e, "Failed to clean temp files")
            }
        }
        
        updateUIState()
    }
    
    private fun updateUIState() {
        binding.apply {
            btnPlayOriginal.isEnabled = selectedAudioFile != null
            btnEncode.isEnabled = selectedAudioFile != null
            btnDecode.isEnabled = encodedCodes != null && encodedCodesS != null
            
            // 检查是否有解码文件
            val hasDecodedFile = filesDir.listFiles { file ->
                file.name.startsWith("decoded_audio_") && file.name.endsWith(".wav")
            }?.isNotEmpty() == true
            
            btnPlayDecoded.isEnabled = hasDecodedFile
        }
    }
    
    private fun stopMediaPlayer() {
        mediaPlayer?.let {
            try {
                if (it.isPlaying) {
                    it.stop()
                }
                it.release()
            } catch (e: Exception) {
                Timber.e(e, "Error stopping media player")
            }
        }
        mediaPlayer = null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopMediaPlayer()
    }
}
