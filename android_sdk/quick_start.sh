#!/bin/bash

# AudioCodec Android SDK 快速开始脚本
# 帮助用户快速设置和测试SDK

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo "=================================================="
    echo "    AudioCodec Android SDK 快速开始向导"
    echo "=================================================="
    echo ""
    echo "本脚本将帮助您："
    echo "1. 检查开发环境"
    echo "2. 验证项目完整性"
    echo "3. 构建示例应用"
    echo "4. 安装到设备进行测试"
    echo ""
}

# 检查开发环境
check_environment() {
    log_info "检查开发环境..."
    
    # 检查Android SDK
    if [ -z "$ANDROID_HOME" ]; then
        log_error "ANDROID_HOME 环境变量未设置"
        log_info "请设置ANDROID_HOME指向您的Android SDK目录"
        exit 1
    fi
    log_success "Android SDK: $ANDROID_HOME"
    
    # 检查必要工具
    tools=("adb" "python3")
    for tool in "${tools[@]}"; do
        if command -v $tool &> /dev/null; then
            log_success "$tool 已安装"
        else
            log_error "$tool 未找到，请先安装"
            exit 1
        fi
    done
    
    # 检查设备连接
    if adb devices | grep -q "device$"; then
        device_count=$(adb devices | grep "device$" | wc -l)
        log_success "检测到 $device_count 个Android设备"
    else
        log_warning "没有检测到Android设备"
        log_info "请连接Android设备并启用USB调试"
        read -p "按Enter键继续（将跳过设备安装步骤）..."
    fi
}

# 检查项目完整性
check_project() {
    log_info "检查项目完整性..."
    
    if [ -f "tools/check_project.py" ]; then
        python3 tools/check_project.py
        if [ $? -eq 0 ]; then
            log_success "项目完整性检查通过"
        else
            log_error "项目完整性检查失败"
            exit 1
        fi
    else
        log_warning "项目检查工具未找到，跳过检查"
    fi
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 检查是否有Gradle Wrapper
    if [ -f "gradlew" ]; then
        GRADLE_CMD="./gradlew"
    elif command -v gradle &> /dev/null; then
        GRADLE_CMD="gradle"
    else
        log_error "Gradle未找到，请安装Gradle或使用Android Studio"
        exit 1
    fi
    
    # 构建库和示例应用
    log_info "构建Android库..."
    $GRADLE_CMD :android_library:assembleDebug
    
    log_info "构建示例应用..."
    $GRADLE_CMD :sample_app:assembleDebug
    
    # 检查构建产物
    APK_PATH="sample_app/build/outputs/apk/debug/sample_app-debug.apk"
    AAR_PATH="android_library/build/outputs/aar/android_library-debug.aar"
    
    if [ -f "$APK_PATH" ]; then
        log_success "示例应用构建成功: $APK_PATH"
    else
        log_error "示例应用构建失败"
        exit 1
    fi
    
    if [ -f "$AAR_PATH" ]; then
        log_success "Android库构建成功: $AAR_PATH"
    else
        log_error "Android库构建失败"
        exit 1
    fi
}

# 安装到设备
install_to_device() {
    APK_PATH="sample_app/build/outputs/apk/debug/sample_app-debug.apk"
    
    if adb devices | grep -q "device$"; then
        log_info "安装示例应用到设备..."
        adb install -r "$APK_PATH"
        
        if [ $? -eq 0 ]; then
            log_success "示例应用安装成功"
            log_info "您可以在设备上找到 'AudioCodec SDK 示例' 应用"
        else
            log_error "示例应用安装失败"
        fi
    else
        log_warning "没有检测到设备，跳过安装步骤"
        log_info "您可以手动安装APK文件: $APK_PATH"
    fi
}

# 显示使用指南
show_usage_guide() {
    echo ""
    echo "=================================================="
    echo "              使用指南"
    echo "=================================================="
    echo ""
    echo "1. 示例应用使用："
    echo "   - 打开设备上的 'AudioCodec SDK 示例' 应用"
    echo "   - 点击 '初始化 SDK' 按钮"
    echo "   - 点击 '编解码测试' 进行功能测试"
    echo ""
    echo "2. 集成到您的项目："
    echo "   - 复制 android_library 模块到您的项目"
    echo "   - 在 settings.gradle 中添加: include ':android_library'"
    echo "   - 在 app/build.gradle 中添加依赖"
    echo ""
    echo "3. 文档参考："
    echo "   - 用户指南: docs/user_guide.md"
    echo "   - 开发者文档: docs/developer_guide.md"
    echo "   - API参考: docs/api_reference.md"
    echo ""
    echo "4. 获取帮助："
    echo "   - 查看示例代码: sample_app/src/main/java/"
    echo "   - 运行项目检查: python3 tools/check_project.py"
    echo "   - 查看构建日志排查问题"
    echo ""
}

# 主函数
main() {
    show_welcome
    
    # 检查是否在正确的目录
    if [ ! -f "settings.gradle" ] || [ ! -d "android_library" ]; then
        log_error "请在AudioCodec Android SDK根目录下运行此脚本"
        exit 1
    fi
    
    # 执行各个步骤
    check_environment
    check_project
    build_project
    install_to_device
    show_usage_guide
    
    log_success "快速开始完成！"
    echo ""
    echo "🎉 恭喜！AudioCodec Android SDK已准备就绪"
    echo "📱 您现在可以开始使用SDK进行音频编解码开发了"
    echo ""
}

# 运行主函数
main "$@"
