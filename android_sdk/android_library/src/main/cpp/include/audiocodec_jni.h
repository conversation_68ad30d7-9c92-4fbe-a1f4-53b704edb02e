#ifndef AUDIOCODEC_JNI_H
#define AUDIOCODEC_JNI_H

#include <jni.h>
#include <string>
#include <vector>
#include <memory>

// JNI函数声明
extern "C" {

/**
 * 初始化音频编解码器
 */
JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_initialize(
    JNIEnv *env, 
    jobject thiz,
    jstring model_path,
    jboolean use_cuda,
    jint sample_rate,
    jint hop_size
);

/**
 * 编码音频文件
 */
JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_encodeAudio(
    JNIEnv *env,
    jobject thiz,
    jstring input_path
);

/**
 * 解码音频数据
 */
JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_decodeAudio(
    JNIEnv *env,
    jobject thiz,
    jbyteArray codes,
    jbyteArray codes_s,
    jstring output_path
);

/**
 * 编码音频数据（内存版本）
 */
JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_encodeAudioData(
    JNIEnv *env,
    jobject thiz,
    jfloatArray audio_data,
    jint sample_rate
);

/**
 * 解码音频数据到内存
 */
JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_decodeAudioData(
    JNIEnv *env,
    jobject thiz,
    jbyteArray codes,
    jbyteArray codes_s
);

/**
 * 获取编解码器信息
 */
JNIEXPORT jstring JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getCodecInfo(
    JNIEnv *env,
    jobject thiz
);

/**
 * 设置日志级别
 */
JNIEXPORT void JNICALL
Java_com_audiocodec_core_AudioCodecJNI_setLogLevel(
    JNIEnv *env,
    jobject thiz,
    jint level
);

/**
 * 释放资源
 */
JNIEXPORT void JNICALL
Java_com_audiocodec_core_AudioCodecJNI_release(
    JNIEnv *env,
    jobject thiz
);

/**
 * 检查是否已初始化
 */
JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_isInitialized(
    JNIEnv *env,
    jobject thiz
);

/**
 * 获取支持的音频格式
 */
JNIEXPORT jobjectArray JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getSupportedFormats(
    JNIEnv *env,
    jobject thiz
);

/**
 * 验证音频文件
 */
JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_validateAudioFile(
    JNIEnv *env,
    jobject thiz,
    jstring file_path
);

/**
 * 获取音频文件信息
 */
JNIEXPORT jstring JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getAudioInfo(
    JNIEnv *env,
    jobject thiz,
    jstring file_path
);

} // extern "C"

// 辅助函数
namespace audiocodec {

/**
 * 创建CodecResult对象
 */
jobject createCodecResult(
    JNIEnv *env,
    bool is_success,
    const std::vector<uint8_t>& codes = {},
    const std::vector<uint8_t>& codes_s = {},
    int compressed_size = 0,
    const std::string& output_path = "",
    long processing_time_ms = 0,
    float audio_duration_sec = 0.0f,
    const std::string& error_message = ""
);

/**
 * 字符串转换辅助函数
 */
std::string jstringToString(JNIEnv *env, jstring jstr);
jstring stringToJstring(JNIEnv *env, const std::string& str);

/**
 * 字节数组转换辅助函数
 */
std::vector<uint8_t> jbyteArrayToVector(JNIEnv *env, jbyteArray jarray);
jbyteArray vectorToJbyteArray(JNIEnv *env, const std::vector<uint8_t>& vec);

/**
 * 浮点数组转换辅助函数
 */
std::vector<float> jfloatArrayToVector(JNIEnv *env, jfloatArray jarray);
jfloatArray vectorToJfloatArray(JNIEnv *env, const std::vector<float>& vec);

} // namespace audiocodec

#endif // AUDIOCODEC_JNI_H
