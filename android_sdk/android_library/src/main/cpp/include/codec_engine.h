#ifndef CODEC_ENGINE_H
#define CODEC_ENGINE_H

#include <string>
#include <vector>
#include <memory>
#include <chrono>

namespace audiocodec {

/**
 * 编解码结果结构
 */
struct CodecResult {
    bool is_success = false;
    std::vector<uint8_t> codes;
    std::vector<uint8_t> codes_s;
    int compressed_size = 0;
    std::string output_path;
    long processing_time_ms = 0;
    float audio_duration_sec = 0.0f;
    std::string error_message;
    std::vector<float> audio_data; // 用于内存版本的音频数据
};

/**
 * 编解码配置
 */
struct CodecConfig {
    bool use_cuda = false;
    int sample_rate = 16000;
    int hop_size = 200;
    int win_size = 800;
    int n_fft = 1024;
    int f_min = 0;
    int f_max = 8000;
    int n_mels = 80;
    float min_db = -115.0f;
    float ref_db = 20.0f;
    float max_abs_value = 1.0f;
};

/**
 * 音频编解码引擎
 */
class CodecEngine {
public:
    CodecEngine();
    ~CodecEngine();

    /**
     * 初始化引擎
     */
    bool initialize(const std::string& model_path, const CodecConfig& config);

    /**
     * 编码音频文件
     */
    CodecResult encodeAudio(const std::string& input_path);

    /**
     * 编码音频数据
     */
    CodecResult encodeAudioData(const std::vector<float>& audio_data, int sample_rate);

    /**
     * 解码音频到文件
     */
    CodecResult decodeAudio(
        const std::vector<uint8_t>& codes,
        const std::vector<uint8_t>& codes_s,
        const std::string& output_path
    );

    /**
     * 解码音频到内存
     */
    CodecResult decodeAudioData(
        const std::vector<uint8_t>& codes,
        const std::vector<uint8_t>& codes_s
    );

    /**
     * 获取编解码器信息
     */
    std::string getCodecInfo() const;

    /**
     * 释放资源
     */
    void release();

    /**
     * 检查是否已初始化
     */
    bool isInitialized() const;

    /**
     * 获取支持的格式
     */
    std::vector<std::string> getSupportedFormats() const;

    /**
     * 验证音频文件
     */
    bool validateAudioFile(const std::string& file_path) const;

    /**
     * 获取音频文件信息
     */
    std::string getAudioInfo(const std::string& file_path) const;

private:
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

/**
 * 全局编解码引擎实例
 */
extern std::unique_ptr<CodecEngine> g_codec_engine;

} // namespace audiocodec

#endif // CODEC_ENGINE_H
