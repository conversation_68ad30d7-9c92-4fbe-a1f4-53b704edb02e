cmake_minimum_required(VERSION 3.22.1)

project("audiocodec")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -frtti -fexceptions")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O3")

# 添加预处理器定义
add_definitions(-DANDROID)
add_definitions(-DUSE_PYTORCH_MOBILE)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/pytorch
    ${CMAKE_CURRENT_SOURCE_DIR}/onnx
)

# 查找必要的库
find_library(log-lib log)
find_library(android-lib android)

# PyTorch Mobile库路径（如果存在）
set(PYTORCH_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/pytorch/include)
set(PYTORCH_LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR}/pytorch/lib/${ANDROID_ABI})

# 检查PyTorch库是否存在
if(EXISTS ${PYTORCH_LIB_DIR}/libpytorch_jni.so)
    # 添加PyTorch库
    add_library(pytorch_jni SHARED IMPORTED)
    set_target_properties(pytorch_jni PROPERTIES
        IMPORTED_LOCATION ${PYTORCH_LIB_DIR}/libpytorch_jni.so)

    add_library(fbjni SHARED IMPORTED)
    set_target_properties(fbjni PROPERTIES
        IMPORTED_LOCATION ${PYTORCH_LIB_DIR}/libfbjni.so)

    set(PYTORCH_AVAILABLE TRUE)
else()
    set(PYTORCH_AVAILABLE FALSE)
    message(WARNING "PyTorch Mobile libraries not found. Please download them manually.")
endif()

# 源文件
set(SOURCES
    audiocodec_jni.cpp
    codec_engine.cpp
    codec_engine_impl.cpp
    audio_processor.cpp
    model_manager.cpp
    utils.cpp
)

# 创建共享库
add_library(audiocodec SHARED ${SOURCES})

# 链接库
if(PYTORCH_AVAILABLE)
    target_link_libraries(audiocodec
        ${log-lib}
        ${android-lib}
        pytorch_jni
        fbjni
    )
else()
    target_link_libraries(audiocodec
        ${log-lib}
        ${android-lib}
    )
endif()

# 包含目录
if(PYTORCH_AVAILABLE)
    target_include_directories(audiocodec PRIVATE
        ${PYTORCH_INCLUDE_DIR}
        ${PYTORCH_INCLUDE_DIR}/torch/csrc/api/include
    )
endif()
