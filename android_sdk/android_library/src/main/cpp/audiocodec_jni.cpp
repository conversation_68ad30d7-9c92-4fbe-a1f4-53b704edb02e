#include "include/audiocodec_jni.h"
#include "include/codec_engine.h"
#include <android/log.h>
#include <chrono>
#include <memory>
#include <cstring>

#define LOG_TAG "AudioCodecJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

using namespace audiocodec;

// 全局编解码引擎实例
std::unique_ptr<CodecEngine> g_codec_engine = nullptr;

extern "C" {

JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_initialize(
    JNIEnv *env, 
    jobject thiz,
    jstring model_path,
    jboolean use_cuda,
    jint sample_rate,
    jint hop_size
) {
    try {
        std::string model_path_str = jstringToString(env, model_path);
        LOGI("Initializing AudioCodec with model path: %s", model_path_str.c_str());
        
        if (!g_codec_engine) {
            g_codec_engine = std::make_unique<CodecEngine>();
        }
        
        CodecConfig config;
        config.use_cuda = use_cuda;
        config.sample_rate = sample_rate;
        config.hop_size = hop_size;
        
        bool result = g_codec_engine->initialize(model_path_str, config);
        
        if (result) {
            LOGI("AudioCodec initialized successfully");
        } else {
            LOGE("Failed to initialize AudioCodec");
        }
        
        return result;
    } catch (const std::exception& e) {
        LOGE("Exception in initialize: %s", e.what());
        return false;
    }
}

JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_encodeAudio(
    JNIEnv *env,
    jobject thiz,
    jstring input_path
) {
    try {
        if (!g_codec_engine || !g_codec_engine->isInitialized()) {
            LOGE("CodecEngine not initialized");
            return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, "Engine not initialized");
        }
        
        std::string input_path_str = jstringToString(env, input_path);
        LOGD("Encoding audio: %s", input_path_str.c_str());
        
        auto start_time = std::chrono::high_resolution_clock::now();
        CodecResult result = g_codec_engine->encodeAudio(input_path_str);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        result.processing_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        LOGD("Encoding completed in %ld ms", result.processing_time_ms);
        
        return createCodecResult(
            env,
            result.is_success,
            result.codes,
            result.codes_s,
            result.compressed_size,
            result.output_path,
            result.processing_time_ms,
            result.audio_duration_sec,
            result.error_message
        );
    } catch (const std::exception& e) {
        LOGE("Exception in encodeAudio: %s", e.what());
        return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, e.what());
    }
}

JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_decodeAudio(
    JNIEnv *env,
    jobject thiz,
    jbyteArray codes,
    jbyteArray codes_s,
    jstring output_path
) {
    try {
        if (!g_codec_engine || !g_codec_engine->isInitialized()) {
            LOGE("CodecEngine not initialized");
            return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, "Engine not initialized");
        }
        
        std::vector<uint8_t> codes_vec = jbyteArrayToVector(env, codes);
        std::vector<uint8_t> codes_s_vec = jbyteArrayToVector(env, codes_s);
        std::string output_path_str = jstringToString(env, output_path);
        
        LOGD("Decoding audio to: %s", output_path_str.c_str());
        
        auto start_time = std::chrono::high_resolution_clock::now();
        CodecResult result = g_codec_engine->decodeAudio(codes_vec, codes_s_vec, output_path_str);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        result.processing_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        LOGD("Decoding completed in %ld ms", result.processing_time_ms);
        
        return createCodecResult(
            env,
            result.is_success,
            result.codes,
            result.codes_s,
            result.compressed_size,
            result.output_path,
            result.processing_time_ms,
            result.audio_duration_sec,
            result.error_message
        );
    } catch (const std::exception& e) {
        LOGE("Exception in decodeAudio: %s", e.what());
        return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, e.what());
    }
}

JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_encodeAudioData(
    JNIEnv *env,
    jobject thiz,
    jfloatArray audio_data,
    jint sample_rate
) {
    try {
        if (!g_codec_engine || !g_codec_engine->isInitialized()) {
            LOGE("CodecEngine not initialized");
            return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, "Engine not initialized");
        }
        
        std::vector<float> audio_vec = jfloatArrayToVector(env, audio_data);
        
        LOGD("Encoding audio data, samples: %zu, sample_rate: %d", audio_vec.size(), sample_rate);
        
        auto start_time = std::chrono::high_resolution_clock::now();
        CodecResult result = g_codec_engine->encodeAudioData(audio_vec, sample_rate);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        result.processing_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        LOGD("Audio data encoding completed in %ld ms", result.processing_time_ms);
        
        return createCodecResult(
            env,
            result.is_success,
            result.codes,
            result.codes_s,
            result.compressed_size,
            result.output_path,
            result.processing_time_ms,
            result.audio_duration_sec,
            result.error_message
        );
    } catch (const std::exception& e) {
        LOGE("Exception in encodeAudioData: %s", e.what());
        return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, e.what());
    }
}

JNIEXPORT jobject JNICALL
Java_com_audiocodec_core_AudioCodecJNI_decodeAudioData(
    JNIEnv *env,
    jobject thiz,
    jbyteArray codes,
    jbyteArray codes_s
) {
    try {
        if (!g_codec_engine || !g_codec_engine->isInitialized()) {
            LOGE("CodecEngine not initialized");
            return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, "Engine not initialized");
        }
        
        std::vector<uint8_t> codes_vec = jbyteArrayToVector(env, codes);
        std::vector<uint8_t> codes_s_vec = jbyteArrayToVector(env, codes_s);
        
        LOGD("Decoding audio data to memory");
        
        auto start_time = std::chrono::high_resolution_clock::now();
        CodecResult result = g_codec_engine->decodeAudioData(codes_vec, codes_s_vec);
        auto end_time = std::chrono::high_resolution_clock::now();
        
        result.processing_time_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            end_time - start_time).count();
        
        LOGD("Audio data decoding completed in %ld ms", result.processing_time_ms);
        
        return createCodecResult(
            env,
            result.is_success,
            result.codes,
            result.codes_s,
            result.compressed_size,
            result.output_path,
            result.processing_time_ms,
            result.audio_duration_sec,
            result.error_message
        );
    } catch (const std::exception& e) {
        LOGE("Exception in decodeAudioData: %s", e.what());
        return createCodecResult(env, false, {}, {}, 0, "", 0, 0.0f, e.what());
    }
}

JNIEXPORT jstring JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getCodecInfo(
    JNIEnv *env,
    jobject thiz
) {
    try {
        if (!g_codec_engine) {
            return stringToJstring(env, "CodecEngine not created");
        }
        
        std::string info = g_codec_engine->getCodecInfo();
        return stringToJstring(env, info);
    } catch (const std::exception& e) {
        LOGE("Exception in getCodecInfo: %s", e.what());
        return stringToJstring(env, "Error getting codec info");
    }
}

JNIEXPORT void JNICALL
Java_com_audiocodec_core_AudioCodecJNI_setLogLevel(
    JNIEnv *env,
    jobject thiz,
    jint level
) {
    // 设置日志级别的实现
    LOGD("Setting log level to: %d", level);
}

JNIEXPORT void JNICALL
Java_com_audiocodec_core_AudioCodecJNI_release(
    JNIEnv *env,
    jobject thiz
) {
    try {
        if (g_codec_engine) {
            g_codec_engine->release();
            g_codec_engine.reset();
            LOGI("AudioCodec engine released");
        }
    } catch (const std::exception& e) {
        LOGE("Exception in release: %s", e.what());
    }
}

JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_isInitialized(
    JNIEnv *env,
    jobject thiz
) {
    return g_codec_engine && g_codec_engine->isInitialized();
}

JNIEXPORT jobjectArray JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getSupportedFormats(
    JNIEnv *env,
    jobject thiz
) {
    try {
        std::vector<std::string> formats;
        if (g_codec_engine) {
            formats = g_codec_engine->getSupportedFormats();
        } else {
            formats = {"wav", "mp3", "m4a", "aac"};
        }
        
        jobjectArray result = env->NewObjectArray(formats.size(), env->FindClass("java/lang/String"), nullptr);
        for (size_t i = 0; i < formats.size(); ++i) {
            env->SetObjectArrayElement(result, i, stringToJstring(env, formats[i]));
        }
        return result;
    } catch (const std::exception& e) {
        LOGE("Exception in getSupportedFormats: %s", e.what());
        return nullptr;
    }
}

JNIEXPORT jboolean JNICALL
Java_com_audiocodec_core_AudioCodecJNI_validateAudioFile(
    JNIEnv *env,
    jobject thiz,
    jstring file_path
) {
    try {
        if (!g_codec_engine) {
            return false;
        }
        
        std::string file_path_str = jstringToString(env, file_path);
        return g_codec_engine->validateAudioFile(file_path_str);
    } catch (const std::exception& e) {
        LOGE("Exception in validateAudioFile: %s", e.what());
        return false;
    }
}

JNIEXPORT jstring JNICALL
Java_com_audiocodec_core_AudioCodecJNI_getAudioInfo(
    JNIEnv *env,
    jobject thiz,
    jstring file_path
) {
    try {
        if (!g_codec_engine) {
            return stringToJstring(env, "{}");
        }
        
        std::string file_path_str = jstringToString(env, file_path);
        std::string info = g_codec_engine->getAudioInfo(file_path_str);
        return stringToJstring(env, info);
    } catch (const std::exception& e) {
        LOGE("Exception in getAudioInfo: %s", e.what());
        return stringToJstring(env, "{}");
    }
}

} // extern "C"

// 辅助函数实现
namespace audiocodec {

jobject createCodecResult(
    JNIEnv *env,
    bool is_success,
    const std::vector<uint8_t>& codes,
    const std::vector<uint8_t>& codes_s,
    int compressed_size,
    const std::string& output_path,
    long processing_time_ms,
    float audio_duration_sec,
    const std::string& error_message
) {
    // 查找CodecResult类
    jclass codecResultClass = env->FindClass("com/audiocodec/model/CodecResult");
    if (!codecResultClass) {
        LOGE("Failed to find CodecResult class");
        return nullptr;
    }

    // 查找构造函数
    jmethodID constructor = env->GetMethodID(codecResultClass, "<init>",
        "(Z[B[BILjava/lang/String;JFLjava/lang/String;Ljava/util/Map;)V");
    if (!constructor) {
        LOGE("Failed to find CodecResult constructor");
        return nullptr;
    }

    // 创建字节数组
    jbyteArray jcodes = nullptr;
    jbyteArray jcodes_s = nullptr;

    if (!codes.empty()) {
        jcodes = vectorToJbyteArray(env, codes);
    }

    if (!codes_s.empty()) {
        jcodes_s = vectorToJbyteArray(env, codes_s);
    }

    // 创建字符串
    jstring joutput_path = output_path.empty() ? nullptr : stringToJstring(env, output_path);
    jstring jerror_message = error_message.empty() ? nullptr : stringToJstring(env, error_message);

    // 创建空的Map
    jclass hashMapClass = env->FindClass("java/util/HashMap");
    jmethodID hashMapConstructor = env->GetMethodID(hashMapClass, "<init>", "()V");
    jobject extraInfo = env->NewObject(hashMapClass, hashMapConstructor);

    // 创建CodecResult对象
    jobject result = env->NewObject(codecResultClass, constructor,
        is_success, jcodes, jcodes_s, compressed_size, joutput_path,
        processing_time_ms, audio_duration_sec, jerror_message, extraInfo);

    return result;
}

std::string jstringToString(JNIEnv *env, jstring jstr) {
    if (!jstr) return "";

    const char* chars = env->GetStringUTFChars(jstr, nullptr);
    std::string result(chars);
    env->ReleaseStringUTFChars(jstr, chars);
    return result;
}

jstring stringToJstring(JNIEnv *env, const std::string& str) {
    return env->NewStringUTF(str.c_str());
}

std::vector<uint8_t> jbyteArrayToVector(JNIEnv *env, jbyteArray jarray) {
    if (!jarray) return {};

    jsize length = env->GetArrayLength(jarray);
    std::vector<uint8_t> result(length);

    jbyte* bytes = env->GetByteArrayElements(jarray, nullptr);
    std::memcpy(result.data(), bytes, length);
    env->ReleaseByteArrayElements(jarray, bytes, JNI_ABORT);

    return result;
}

jbyteArray vectorToJbyteArray(JNIEnv *env, const std::vector<uint8_t>& vec) {
    jbyteArray result = env->NewByteArray(vec.size());
    env->SetByteArrayRegion(result, 0, vec.size(), reinterpret_cast<const jbyte*>(vec.data()));
    return result;
}

std::vector<float> jfloatArrayToVector(JNIEnv *env, jfloatArray jarray) {
    if (!jarray) return {};

    jsize length = env->GetArrayLength(jarray);
    std::vector<float> result(length);

    jfloat* floats = env->GetFloatArrayElements(jarray, nullptr);
    std::memcpy(result.data(), floats, length * sizeof(float));
    env->ReleaseFloatArrayElements(jarray, floats, JNI_ABORT);

    return result;
}

jfloatArray vectorToJfloatArray(JNIEnv *env, const std::vector<float>& vec) {
    jfloatArray result = env->NewFloatArray(vec.size());
    env->SetFloatArrayRegion(result, 0, vec.size(), vec.data());
    return result;
}

} // namespace audiocodec
