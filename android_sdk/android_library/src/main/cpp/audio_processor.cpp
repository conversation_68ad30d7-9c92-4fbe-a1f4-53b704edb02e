#include <android/log.h>
#include <vector>
#include <cmath>
#include <algorithm>

#define LOG_TAG "AudioProcessor"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace audiocodec {

/**
 * 音频处理工具类
 */
class AudioProcessor {
public:
    /**
     * 应用汉明窗
     */
    static std::vector<float> applyHammingWindow(const std::vector<float>& signal) {
        std::vector<float> windowed(signal.size());
        for (size_t i = 0; i < signal.size(); ++i) {
            float w = 0.54f - 0.46f * std::cos(2.0f * M_PI * i / (signal.size() - 1));
            windowed[i] = signal[i] * w;
        }
        return windowed;
    }
    
    /**
     * 预加重滤波器
     */
    static std::vector<float> preEmphasis(const std::vector<float>& signal, float coeff = 0.97f) {
        if (signal.empty()) return {};
        
        std::vector<float> emphasized(signal.size());
        emphasized[0] = signal[0];
        
        for (size_t i = 1; i < signal.size(); ++i) {
            emphasized[i] = signal[i] - coeff * signal[i - 1];
        }
        
        return emphasized;
    }
    
    /**
     * 音频归一化
     */
    static std::vector<float> normalize(const std::vector<float>& signal, float target_level = 0.95f) {
        if (signal.empty()) return {};
        
        // 找到最大绝对值
        float max_val = 0.0f;
        for (float sample : signal) {
            max_val = std::max(max_val, std::abs(sample));
        }
        
        if (max_val == 0.0f) return signal;
        
        // 归一化
        float scale = target_level / max_val;
        std::vector<float> normalized(signal.size());
        
        for (size_t i = 0; i < signal.size(); ++i) {
            normalized[i] = signal[i] * scale;
        }
        
        return normalized;
    }
    
    /**
     * 音频静音检测
     */
    static bool isSilent(const std::vector<float>& signal, float threshold = 0.01f) {
        if (signal.empty()) return true;
        
        float rms = 0.0f;
        for (float sample : signal) {
            rms += sample * sample;
        }
        rms = std::sqrt(rms / signal.size());
        
        return rms < threshold;
    }
    
    /**
     * 移除静音段
     */
    static std::vector<float> trimSilence(const std::vector<float>& signal, 
                                         float threshold = 0.01f, 
                                         int frame_length = 2048) {
        if (signal.empty()) return {};
        
        // 找到开始和结束的非静音位置
        int start = 0;
        int end = signal.size();
        
        // 从开头找非静音
        for (int i = 0; i < static_cast<int>(signal.size()) - frame_length; i += frame_length) {
            std::vector<float> frame(signal.begin() + i, 
                                   signal.begin() + std::min(i + frame_length, static_cast<int>(signal.size())));
            if (!isSilent(frame, threshold)) {
                start = i;
                break;
            }
        }
        
        // 从结尾找非静音
        for (int i = signal.size() - frame_length; i >= 0; i -= frame_length) {
            std::vector<float> frame(signal.begin() + i, 
                                   signal.begin() + std::min(i + frame_length, static_cast<int>(signal.size())));
            if (!isSilent(frame, threshold)) {
                end = i + frame_length;
                break;
            }
        }
        
        if (start >= end) return {};
        
        return std::vector<float>(signal.begin() + start, signal.begin() + end);
    }
    
    /**
     * 简单的低通滤波器
     */
    static std::vector<float> lowPassFilter(const std::vector<float>& signal, float cutoff_freq, float sample_rate) {
        if (signal.empty()) return {};
        
        // 简单的一阶低通滤波器
        float rc = 1.0f / (2.0f * M_PI * cutoff_freq);
        float dt = 1.0f / sample_rate;
        float alpha = dt / (rc + dt);
        
        std::vector<float> filtered(signal.size());
        filtered[0] = signal[0];
        
        for (size_t i = 1; i < signal.size(); ++i) {
            filtered[i] = alpha * signal[i] + (1.0f - alpha) * filtered[i - 1];
        }
        
        return filtered;
    }
    
    /**
     * 计算音频的RMS能量
     */
    static float calculateRMS(const std::vector<float>& signal) {
        if (signal.empty()) return 0.0f;
        
        float sum = 0.0f;
        for (float sample : signal) {
            sum += sample * sample;
        }
        
        return std::sqrt(sum / signal.size());
    }
    
    /**
     * 音频分帧
     */
    static std::vector<std::vector<float>> frameSignal(const std::vector<float>& signal, 
                                                      int frame_length, 
                                                      int hop_length) {
        if (signal.empty() || frame_length <= 0 || hop_length <= 0) {
            return {};
        }
        
        std::vector<std::vector<float>> frames;
        
        for (int start = 0; start + frame_length <= static_cast<int>(signal.size()); start += hop_length) {
            std::vector<float> frame(signal.begin() + start, signal.begin() + start + frame_length);
            frames.push_back(frame);
        }
        
        return frames;
    }
    
    /**
     * 重叠相加重建信号
     */
    static std::vector<float> overlapAdd(const std::vector<std::vector<float>>& frames, 
                                        int hop_length) {
        if (frames.empty()) return {};
        
        int frame_length = frames[0].size();
        int total_length = (frames.size() - 1) * hop_length + frame_length;
        
        std::vector<float> signal(total_length, 0.0f);
        
        for (size_t i = 0; i < frames.size(); ++i) {
            int start = i * hop_length;
            for (size_t j = 0; j < frames[i].size() && start + j < signal.size(); ++j) {
                signal[start + j] += frames[i][j];
            }
        }
        
        return signal;
    }
    
    /**
     * 计算零交叉率
     */
    static float calculateZeroCrossingRate(const std::vector<float>& signal) {
        if (signal.size() < 2) return 0.0f;
        
        int crossings = 0;
        for (size_t i = 1; i < signal.size(); ++i) {
            if ((signal[i] >= 0) != (signal[i - 1] >= 0)) {
                crossings++;
            }
        }
        
        return static_cast<float>(crossings) / (signal.size() - 1);
    }
    
    /**
     * 音频重采样（简单线性插值）
     */
    static std::vector<float> resample(const std::vector<float>& signal, 
                                      int original_rate, 
                                      int target_rate) {
        if (signal.empty() || original_rate <= 0 || target_rate <= 0) {
            return {};
        }
        
        if (original_rate == target_rate) {
            return signal;
        }
        
        double ratio = static_cast<double>(target_rate) / original_rate;
        size_t new_length = static_cast<size_t>(signal.size() * ratio);
        
        std::vector<float> resampled(new_length);
        
        for (size_t i = 0; i < new_length; ++i) {
            double src_index = i / ratio;
            size_t index = static_cast<size_t>(src_index);
            double frac = src_index - index;
            
            if (index + 1 < signal.size()) {
                resampled[i] = signal[index] * (1.0 - frac) + signal[index + 1] * frac;
            } else if (index < signal.size()) {
                resampled[i] = signal[index];
            } else {
                resampled[i] = 0.0f;
            }
        }
        
        return resampled;
    }
};

} // namespace audiocodec
