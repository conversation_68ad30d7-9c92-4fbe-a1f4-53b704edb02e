#include "include/codec_engine.h"
#include <android/log.h>
#include <fstream>
#include <sstream>
#include <cmath>

#define LOG_TAG "CodecEngine"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace audiocodec {

// 全局编解码引擎实例
std::unique_ptr<CodecEngine> g_codec_engine = nullptr;

class CodecEngine::Impl {
public:
    Impl() : initialized_(false) {}
    
    ~Impl() {
        release();
    }
    
    bool initialize(const std::string& model_path, const CodecConfig& config) {
        try {
            LOGI("Initializing CodecEngine with model path: %s", model_path.c_str());
            
            model_path_ = model_path;
            config_ = config;
            
            // 检查模型文件是否存在
            if (!checkModelFiles()) {
                LOGE("Model files not found or invalid");
                return false;
            }
            
            // 初始化PyTorch模型
            if (!initializePyTorchModels()) {
                LOGE("Failed to initialize PyTorch models");
                return false;
            }
            
            // 初始化音频处理器
            if (!initializeAudioProcessor()) {
                LOGE("Failed to initialize audio processor");
                return false;
            }
            
            initialized_ = true;
            LOGI("CodecEngine initialized successfully");
            return true;
            
        } catch (const std::exception& e) {
            LOGE("Exception during initialization: %s", e.what());
            return false;
        }
    }
    
    CodecResult encodeAudio(const std::string& input_path) {
        CodecResult result;
        
        if (!initialized_) {
            result.error_message = "Engine not initialized";
            return result;
        }
        
        try {
            // 读取音频文件
            std::vector<float> audio_data;
            int sample_rate;
            if (!loadAudioFile(input_path, audio_data, sample_rate)) {
                result.error_message = "Failed to load audio file";
                return result;
            }
            
            // 执行编码
            return encodeAudioData(audio_data, sample_rate);
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            return result;
        }
    }
    
    CodecResult encodeAudioData(const std::vector<float>& audio_data, int sample_rate) {
        CodecResult result;
        
        if (!initialized_) {
            result.error_message = "Engine not initialized";
            return result;
        }
        
        try {
            LOGD("Encoding audio data: %zu samples at %d Hz", audio_data.size(), sample_rate);
            
            // 重采样到目标采样率
            std::vector<float> resampled_audio = resampleAudio(audio_data, sample_rate, config_.sample_rate);
            
            // 提取mel频谱
            std::vector<std::vector<float>> mel_spectrogram = extractMelSpectrogram(resampled_audio);
            
            // 使用PyTorch模型进行编码
            auto [codes, codes_s] = encodeMelSpectrogram(mel_spectrogram);
            
            // 计算压缩大小
            int compressed_size = (codes.size() + codes_s.size()) * 13; // 根据原始代码
            
            // 计算音频时长
            float duration = static_cast<float>(audio_data.size()) / sample_rate;
            
            result.is_success = true;
            result.codes = codes;
            result.codes_s = codes_s;
            result.compressed_size = compressed_size;
            result.audio_duration_sec = duration;
            
            LOGD("Encoding successful: %d bytes compressed", compressed_size);
            return result;
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            LOGE("Encoding failed: %s", e.what());
            return result;
        }
    }
    
    CodecResult decodeAudio(
        const std::vector<uint8_t>& codes,
        const std::vector<uint8_t>& codes_s,
        const std::string& output_path
    ) {
        CodecResult result;
        
        if (!initialized_) {
            result.error_message = "Engine not initialized";
            return result;
        }
        
        try {
            LOGD("Decoding audio to: %s", output_path.c_str());
            
            // 解码到内存
            CodecResult memory_result = decodeAudioData(codes, codes_s);
            if (!memory_result.is_success) {
                return memory_result;
            }
            
            // 保存到文件
            if (!saveAudioFile(output_path, memory_result.audio_data, config_.sample_rate)) {
                result.error_message = "Failed to save audio file";
                return result;
            }
            
            result.is_success = true;
            result.output_path = output_path;
            result.audio_duration_sec = memory_result.audio_duration_sec;
            
            LOGD("Decoding successful, saved to: %s", output_path.c_str());
            return result;
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            LOGE("Decoding failed: %s", e.what());
            return result;
        }
    }
    
    CodecResult decodeAudioData(
        const std::vector<uint8_t>& codes,
        const std::vector<uint8_t>& codes_s
    ) {
        CodecResult result;
        
        if (!initialized_) {
            result.error_message = "Engine not initialized";
            return result;
        }
        
        try {
            LOGD("Decoding audio data to memory");
            
            // 使用PyTorch模型解码mel频谱
            std::vector<std::vector<float>> mel_spectrogram = decodeMelSpectrogram(codes, codes_s);
            
            // 生成音高信息
            std::vector<float> pitch = generatePitch(mel_spectrogram);
            
            // 重建音频
            std::vector<float> audio_data = synthesizeAudio(mel_spectrogram, pitch);
            
            // 计算音频时长
            float duration = static_cast<float>(audio_data.size()) / config_.sample_rate;
            
            result.is_success = true;
            result.audio_data = audio_data;
            result.audio_duration_sec = duration;
            
            LOGD("Decoding to memory successful: %zu samples, %.2f seconds", 
                 audio_data.size(), duration);
            return result;
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            LOGE("Decoding to memory failed: %s", e.what());
            return result;
        }
    }
    
    std::string getCodecInfo() const {
        std::ostringstream info;
        info << "AudioCodec Engine v1.0\n";
        info << "Initialized: " << (initialized_ ? "Yes" : "No") << "\n";
        info << "Model Path: " << model_path_ << "\n";
        info << "Sample Rate: " << config_.sample_rate << "\n";
        info << "Hop Size: " << config_.hop_size << "\n";
        info << "Use CUDA: " << (config_.use_cuda ? "Yes" : "No") << "\n";
        return info.str();
    }
    
    void release() {
        if (initialized_) {
            // 释放PyTorch模型资源
            releasePyTorchModels();
            
            // 释放音频处理器资源
            releaseAudioProcessor();
            
            initialized_ = false;
            LOGI("CodecEngine resources released");
        }
    }
    
    bool isInitialized() const {
        return initialized_;
    }
    
    std::vector<std::string> getSupportedFormats() const {
        return {"wav", "mp3", "m4a", "aac", "flac"};
    }
    
    bool validateAudioFile(const std::string& file_path) const {
        // 检查文件是否存在
        std::ifstream file(file_path);
        if (!file.good()) {
            return false;
        }
        
        // 检查文件扩展名
        std::string extension = getFileExtension(file_path);
        auto supported_formats = getSupportedFormats();
        
        return std::find(supported_formats.begin(), supported_formats.end(), extension) 
               != supported_formats.end();
    }
    
    std::string getAudioInfo(const std::string& file_path) const {
        // 返回JSON格式的音频信息
        std::ostringstream json;
        json << "{";
        json << "\"file_path\":\"" << file_path << "\",";
        json << "\"exists\":" << (validateAudioFile(file_path) ? "true" : "false") << ",";
        json << "\"format\":\"" << getFileExtension(file_path) << "\"";
        json << "}";
        return json.str();
    }

private:
    bool initialized_;
    std::string model_path_;
    CodecConfig config_;
    
    // 私有方法声明
    bool checkModelFiles();
    bool initializePyTorchModels();
    bool initializeAudioProcessor();
    void releasePyTorchModels();
    void releaseAudioProcessor();
    
    bool loadAudioFile(const std::string& path, std::vector<float>& audio_data, int& sample_rate);
    bool saveAudioFile(const std::string& path, const std::vector<float>& audio_data, int sample_rate);
    
    std::vector<float> resampleAudio(const std::vector<float>& audio, int from_sr, int to_sr);
    std::vector<std::vector<float>> extractMelSpectrogram(const std::vector<float>& audio);
    
    std::pair<std::vector<uint8_t>, std::vector<uint8_t>> encodeMelSpectrogram(
        const std::vector<std::vector<float>>& mel);
    std::vector<std::vector<float>> decodeMelSpectrogram(
        const std::vector<uint8_t>& codes, const std::vector<uint8_t>& codes_s);
    
    std::vector<float> generatePitch(const std::vector<std::vector<float>>& mel);
    std::vector<float> synthesizeAudio(
        const std::vector<std::vector<float>>& mel, const std::vector<float>& pitch);
    
    std::string getFileExtension(const std::string& file_path) const;
};

// CodecEngine公共接口实现
CodecEngine::CodecEngine() : pImpl(std::make_unique<Impl>()) {}

CodecEngine::~CodecEngine() = default;

bool CodecEngine::initialize(const std::string& model_path, const CodecConfig& config) {
    return pImpl->initialize(model_path, config);
}

CodecResult CodecEngine::encodeAudio(const std::string& input_path) {
    return pImpl->encodeAudio(input_path);
}

CodecResult CodecEngine::encodeAudioData(const std::vector<float>& audio_data, int sample_rate) {
    return pImpl->encodeAudioData(audio_data, sample_rate);
}

CodecResult CodecEngine::decodeAudio(
    const std::vector<uint8_t>& codes,
    const std::vector<uint8_t>& codes_s,
    const std::string& output_path
) {
    return pImpl->decodeAudio(codes, codes_s, output_path);
}

CodecResult CodecEngine::decodeAudioData(
    const std::vector<uint8_t>& codes,
    const std::vector<uint8_t>& codes_s
) {
    return pImpl->decodeAudioData(codes, codes_s);
}

std::string CodecEngine::getCodecInfo() const {
    return pImpl->getCodecInfo();
}

void CodecEngine::release() {
    pImpl->release();
}

bool CodecEngine::isInitialized() const {
    return pImpl->isInitialized();
}

std::vector<std::string> CodecEngine::getSupportedFormats() const {
    return pImpl->getSupportedFormats();
}

bool CodecEngine::validateAudioFile(const std::string& file_path) const {
    return pImpl->validateAudioFile(file_path);
}

std::string CodecEngine::getAudioInfo(const std::string& file_path) const {
    return pImpl->getAudioInfo(file_path);
}

} // namespace audiocodec
