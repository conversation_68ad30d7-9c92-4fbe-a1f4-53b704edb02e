#include <android/log.h>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <cmath>
#include <algorithm>

#define LOG_TAG "AudioCodecUtils"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)

namespace audiocodec {

/**
 * 工具函数集合
 */
class Utils {
public:
    /**
     * 获取当前时间戳（毫秒）
     */
    static long getCurrentTimeMs() {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    static std::string bytesToHex(const std::vector<uint8_t>& bytes) {
        std::ostringstream oss;
        oss << std::hex << std::setfill('0');
        for (uint8_t byte : bytes) {
            oss << std::setw(2) << static_cast<int>(byte);
        }
        return oss.str();
    }
    
    /**
     * 十六进制字符串转字节数组
     */
    static std::vector<uint8_t> hexToBytes(const std::string& hex) {
        std::vector<uint8_t> bytes;
        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byteString = hex.substr(i, 2);
            uint8_t byte = static_cast<uint8_t>(std::strtol(byteString.c_str(), nullptr, 16));
            bytes.push_back(byte);
        }
        return bytes;
    }
    
    /**
     * 计算数组的均值
     */
    static float calculateMean(const std::vector<float>& data) {
        if (data.empty()) return 0.0f;
        
        float sum = 0.0f;
        for (float value : data) {
            sum += value;
        }
        return sum / data.size();
    }
    
    /**
     * 计算数组的标准差
     */
    static float calculateStdDev(const std::vector<float>& data) {
        if (data.size() < 2) return 0.0f;
        
        float mean = calculateMean(data);
        float sum_sq_diff = 0.0f;
        
        for (float value : data) {
            float diff = value - mean;
            sum_sq_diff += diff * diff;
        }
        
        return std::sqrt(sum_sq_diff / (data.size() - 1));
    }
    
    /**
     * 数组归一化到指定范围
     */
    static std::vector<float> normalizeToRange(const std::vector<float>& data, 
                                              float min_val = -1.0f, 
                                              float max_val = 1.0f) {
        if (data.empty()) return {};
        
        // 找到最小值和最大值
        float data_min = *std::min_element(data.begin(), data.end());
        float data_max = *std::max_element(data.begin(), data.end());
        
        if (data_max == data_min) {
            return std::vector<float>(data.size(), (min_val + max_val) / 2.0f);
        }
        
        std::vector<float> normalized(data.size());
        float scale = (max_val - min_val) / (data_max - data_min);
        
        for (size_t i = 0; i < data.size(); ++i) {
            normalized[i] = min_val + (data[i] - data_min) * scale;
        }
        
        return normalized;
    }
    
    /**
     * Z-score标准化
     */
    static std::vector<float> zScoreNormalize(const std::vector<float>& data) {
        if (data.empty()) return {};
        
        float mean = calculateMean(data);
        float std_dev = calculateStdDev(data);
        
        if (std_dev == 0.0f) {
            return std::vector<float>(data.size(), 0.0f);
        }
        
        std::vector<float> normalized(data.size());
        for (size_t i = 0; i < data.size(); ++i) {
            normalized[i] = (data[i] - mean) / std_dev;
        }
        
        return normalized;
    }
    
    /**
     * 线性插值
     */
    static float linearInterpolate(float x0, float y0, float x1, float y1, float x) {
        if (x1 == x0) return y0;
        return y0 + (y1 - y0) * (x - x0) / (x1 - x0);
    }
    
    /**
     * 数组线性插值重采样
     */
    static std::vector<float> resampleLinear(const std::vector<float>& data, size_t new_size) {
        if (data.empty() || new_size == 0) return {};
        if (data.size() == new_size) return data;
        
        std::vector<float> resampled(new_size);
        float scale = static_cast<float>(data.size() - 1) / (new_size - 1);
        
        for (size_t i = 0; i < new_size; ++i) {
            float src_index = i * scale;
            size_t index = static_cast<size_t>(src_index);
            float frac = src_index - index;
            
            if (index + 1 < data.size()) {
                resampled[i] = data[index] * (1.0f - frac) + data[index + 1] * frac;
            } else {
                resampled[i] = data[index];
            }
        }
        
        return resampled;
    }
    
    /**
     * 计算两个数组的相关系数
     */
    static float calculateCorrelation(const std::vector<float>& x, const std::vector<float>& y) {
        if (x.size() != y.size() || x.empty()) return 0.0f;
        
        float mean_x = calculateMean(x);
        float mean_y = calculateMean(y);
        
        float numerator = 0.0f;
        float sum_sq_x = 0.0f;
        float sum_sq_y = 0.0f;
        
        for (size_t i = 0; i < x.size(); ++i) {
            float diff_x = x[i] - mean_x;
            float diff_y = y[i] - mean_y;
            
            numerator += diff_x * diff_y;
            sum_sq_x += diff_x * diff_x;
            sum_sq_y += diff_y * diff_y;
        }
        
        float denominator = std::sqrt(sum_sq_x * sum_sq_y);
        return (denominator == 0.0f) ? 0.0f : numerator / denominator;
    }
    
    /**
     * 应用滑动平均滤波
     */
    static std::vector<float> movingAverage(const std::vector<float>& data, int window_size) {
        if (data.empty() || window_size <= 0) return data;
        if (window_size == 1) return data;
        
        std::vector<float> filtered(data.size());
        int half_window = window_size / 2;
        
        for (size_t i = 0; i < data.size(); ++i) {
            float sum = 0.0f;
            int count = 0;
            
            int start = std::max(0, static_cast<int>(i) - half_window);
            int end = std::min(static_cast<int>(data.size()), static_cast<int>(i) + half_window + 1);
            
            for (int j = start; j < end; ++j) {
                sum += data[j];
                count++;
            }
            
            filtered[i] = sum / count;
        }
        
        return filtered;
    }
    
    /**
     * 计算信噪比（SNR）
     */
    static float calculateSNR(const std::vector<float>& signal, const std::vector<float>& noise) {
        if (signal.size() != noise.size() || signal.empty()) return 0.0f;
        
        float signal_power = 0.0f;
        float noise_power = 0.0f;
        
        for (size_t i = 0; i < signal.size(); ++i) {
            signal_power += signal[i] * signal[i];
            noise_power += noise[i] * noise[i];
        }
        
        signal_power /= signal.size();
        noise_power /= noise.size();
        
        if (noise_power == 0.0f) return INFINITY;
        
        return 10.0f * std::log10(signal_power / noise_power);
    }
    
    /**
     * 字符串分割
     */
    static std::vector<std::string> split(const std::string& str, char delimiter) {
        std::vector<std::string> tokens;
        std::stringstream ss(str);
        std::string token;
        
        while (std::getline(ss, token, delimiter)) {
            tokens.push_back(token);
        }
        
        return tokens;
    }
    
    /**
     * 字符串去除空白字符
     */
    static std::string trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\n\r");
        if (start == std::string::npos) return "";
        
        size_t end = str.find_last_not_of(" \t\n\r");
        return str.substr(start, end - start + 1);
    }
    
    /**
     * 检查文件扩展名
     */
    static std::string getFileExtension(const std::string& filename) {
        size_t dot_pos = filename.find_last_of('.');
        if (dot_pos == std::string::npos) return "";
        
        std::string ext = filename.substr(dot_pos + 1);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        return ext;
    }
    
    /**
     * 格式化文件大小
     */
    static std::string formatFileSize(long bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        int unit_index = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unit_index < 4) {
            size /= 1024.0;
            unit_index++;
        }
        
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
        return oss.str();
    }
    
    /**
     * 格式化时间持续时间
     */
    static std::string formatDuration(long milliseconds) {
        long seconds = milliseconds / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        seconds %= 60;
        minutes %= 60;
        
        std::ostringstream oss;
        if (hours > 0) {
            oss << hours << "h " << minutes << "m " << seconds << "s";
        } else if (minutes > 0) {
            oss << minutes << "m " << seconds << "s";
        } else {
            oss << seconds << "." << std::setfill('0') << std::setw(3) 
                << (milliseconds % 1000) << "s";
        }
        
        return oss.str();
    }
};

} // namespace audiocodec
