package com.audiocodec.callback

import com.audiocodec.model.CodecResult

/**
 * 编解码回调接口
 */
interface CodecCallback {
    /**
     * 操作成功回调
     * @param result 操作结果
     */
    fun onSuccess(result: CodecResult)
    
    /**
     * 操作失败回调
     * @param error 错误信息
     */
    fun onError(error: String)
    
    /**
     * 进度更新回调（可选实现）
     * @param progress 进度百分比 (0-100)
     */
    fun onProgress(progress: Int) {
        // 默认空实现
    }
}

/**
 * 简化的回调接口，使用lambda表达式
 */
class SimpleCodecCallback(
    private val onSuccessCallback: (CodecResult) -> Unit,
    private val onErrorCallback: (String) -> Unit,
    private val onProgressCallback: ((Int) -> Unit)? = null
) : CodecCallback {
    
    override fun onSuccess(result: CodecResult) {
        onSuccessCallback(result)
    }
    
    override fun onError(error: String) {
        onErrorCallback(error)
    }
    
    override fun onProgress(progress: Int) {
        onProgressCallback?.invoke(progress)
    }
}
