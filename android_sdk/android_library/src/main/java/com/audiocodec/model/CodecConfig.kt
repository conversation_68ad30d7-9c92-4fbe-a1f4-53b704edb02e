package com.audiocodec.model

/**
 * 音频编解码配置类
 */
data class CodecConfig(
    /**
     * 是否使用CUDA加速（Android上通常为false）
     */
    val useCuda: Boolean = false,
    
    /**
     * 音频采样率
     */
    val sampleRate: Int = 16000,
    
    /**
     * 跳跃大小
     */
    val hopSize: Int = 200,
    
    /**
     * 窗口大小
     */
    val winSize: Int = 800,
    
    /**
     * FFT大小
     */
    val nFft: Int = 1024,
    
    /**
     * 最小频率
     */
    val fMin: Int = 0,
    
    /**
     * 最大频率
     */
    val fMax: Int = 8000,
    
    /**
     * Mel频谱通道数
     */
    val nMels: Int = 80,
    
    /**
     * 最小dB值
     */
    val minDb: Float = -115f,
    
    /**
     * 参考dB值
     */
    val refDb: Float = 20f,
    
    /**
     * 最大绝对值
     */
    val maxAbsValue: Float = 1.0f
) {
    companion object {
        /**
         * 创建16kHz配置
         */
        fun create16kConfig() = CodecConfig(
            sampleRate = 16000,
            hopSize = 200,
            winSize = 800,
            nFft = 1024,
            fMax = 8000
        )
        
        /**
         * 创建48kHz配置
         */
        fun create48kConfig() = CodecConfig(
            sampleRate = 48000,
            hopSize = 600,
            winSize = 2400,
            nFft = 4096,
            fMax = 24000
        )
    }
}
