package com.audiocodec.model

/**
 * 编解码结果类
 */
data class CodecResult(
    /**
     * 操作是否成功
     */
    val isSuccess: Boolean,
    
    /**
     * 编码数据（编码时使用）
     */
    val codes: ByteArray? = null,
    
    /**
     * 辅助编码数据（编码时使用）
     */
    val codesS: ByteArray? = null,
    
    /**
     * 压缩后的数据大小（比特）
     */
    val compressedSize: Int = 0,
    
    /**
     * 输出文件路径（解码时使用）
     */
    val outputPath: String? = null,
    
    /**
     * 处理耗时（毫秒）
     */
    val processingTimeMs: Long = 0,
    
    /**
     * 音频时长（秒）
     */
    val audioDurationSec: Float = 0f,
    
    /**
     * 错误信息
     */
    val errorMessage: String? = null,
    
    /**
     * 额外信息
     */
    val extraInfo: Map<String, Any> = emptyMap()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CodecResult

        if (isSuccess != other.isSuccess) return false
        if (codes != null) {
            if (other.codes == null) return false
            if (!codes.contentEquals(other.codes)) return false
        } else if (other.codes != null) return false
        if (codesS != null) {
            if (other.codesS == null) return false
            if (!codesS.contentEquals(other.codesS)) return false
        } else if (other.codesS != null) return false
        if (compressedSize != other.compressedSize) return false
        if (outputPath != other.outputPath) return false
        if (processingTimeMs != other.processingTimeMs) return false
        if (audioDurationSec != other.audioDurationSec) return false
        if (errorMessage != other.errorMessage) return false
        if (extraInfo != other.extraInfo) return false

        return true
    }

    override fun hashCode(): Int {
        var result = isSuccess.hashCode()
        result = 31 * result + (codes?.contentHashCode() ?: 0)
        result = 31 * result + (codesS?.contentHashCode() ?: 0)
        result = 31 * result + compressedSize
        result = 31 * result + (outputPath?.hashCode() ?: 0)
        result = 31 * result + processingTimeMs.hashCode()
        result = 31 * result + audioDurationSec.hashCode()
        result = 31 * result + (errorMessage?.hashCode() ?: 0)
        result = 31 * result + extraInfo.hashCode()
        return result
    }
    
    companion object {
        /**
         * 创建成功的编码结果
         */
        fun encodeSuccess(
            codes: ByteArray,
            codesS: ByteArray,
            compressedSize: Int,
            processingTimeMs: Long,
            audioDurationSec: Float
        ) = CodecResult(
            isSuccess = true,
            codes = codes,
            codesS = codesS,
            compressedSize = compressedSize,
            processingTimeMs = processingTimeMs,
            audioDurationSec = audioDurationSec
        )
        
        /**
         * 创建成功的解码结果
         */
        fun decodeSuccess(
            outputPath: String,
            processingTimeMs: Long,
            audioDurationSec: Float
        ) = CodecResult(
            isSuccess = true,
            outputPath = outputPath,
            processingTimeMs = processingTimeMs,
            audioDurationSec = audioDurationSec
        )
        
        /**
         * 创建失败结果
         */
        fun failure(errorMessage: String) = CodecResult(
            isSuccess = false,
            errorMessage = errorMessage
        )
    }
}
