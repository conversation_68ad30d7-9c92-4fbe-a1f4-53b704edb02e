package com.audiocodec

import android.content.Context
import android.util.Log
import com.audiocodec.core.AudioCodecJNI
import com.audiocodec.model.CodecConfig
import com.audiocodec.model.CodecResult
import com.audiocodec.callback.CodecCallback
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream

/**
 * Audio Codec SDK主入口类
 * 提供音频编解码的核心功能
 */
class AudioCodecSDK private constructor() {
    
    companion object {
        private const val TAG = "AudioCodecSDK"
        
        @Volatile
        private var INSTANCE: AudioCodecSDK? = null
        
        /**
         * 获取SDK单例实例
         */
        fun getInstance(): AudioCodecSDK {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AudioCodecSDK().also { INSTANCE = it }
            }
        }
        
        // 加载native库
        init {
            try {
                System.loadLibrary("audiocodec")
                Timber.d("Native library loaded successfully")
            } catch (e: UnsatisfiedLinkError) {
                Timber.e(e, "Failed to load native library")
            }
        }
    }
    
    private var isInitialized = false
    private val codecScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    /**
     * 初始化SDK
     * @param context Android上下文
     * @param config 编解码配置
     * @return 是否初始化成功
     */
    suspend fun initialize(context: Context, config: CodecConfig = CodecConfig()): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (isInitialized) {
                    Timber.w("SDK already initialized")
                    return@withContext true
                }
                
                // 复制模型文件到内部存储
                val modelDir = copyModelsToInternalStorage(context)
                
                // 初始化native层
                val result = AudioCodecJNI.initialize(
                    modelDir.absolutePath,
                    config.useCuda,
                    config.sampleRate,
                    config.hopSize
                )
                
                if (result) {
                    isInitialized = true
                    Timber.i("AudioCodec SDK initialized successfully")
                } else {
                    Timber.e("Failed to initialize AudioCodec SDK")
                }
                
                result
            } catch (e: Exception) {
                Timber.e(e, "Exception during SDK initialization")
                false
            }
        }
    }
    
    /**
     * 编码音频文件
     * @param inputPath 输入音频文件路径
     * @param callback 编码回调
     */
    fun encodeAudio(inputPath: String, callback: CodecCallback) {
        codecScope.launch {
            try {
                val result = AudioCodecJNI.encodeAudio(inputPath)
                withContext(Dispatchers.Main) {
                    if (result.isSuccess) {
                        callback.onSuccess(result)
                    } else {
                        callback.onError("Encoding failed: ${result.errorMessage}")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    callback.onError("Encoding exception: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 解码音频数据
     * @param codes 编码数据
     * @param codesS 辅助编码数据
     * @param outputPath 输出音频文件路径
     * @param callback 解码回调
     */
    fun decodeAudio(codes: ByteArray, codesS: ByteArray, outputPath: String, callback: CodecCallback) {
        codecScope.launch {
            try {
                val result = AudioCodecJNI.decodeAudio(codes, codesS, outputPath)
                withContext(Dispatchers.Main) {
                    if (result.isSuccess) {
                        callback.onSuccess(result)
                    } else {
                        callback.onError("Decoding failed: ${result.errorMessage}")
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    callback.onError("Decoding exception: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 释放SDK资源
     */
    fun release() {
        codecScope.cancel()
        if (isInitialized) {
            AudioCodecJNI.release()
            isInitialized = false
            Timber.i("AudioCodec SDK released")
        }
    }
    
    /**
     * 复制模型文件到内部存储
     */
    private suspend fun copyModelsToInternalStorage(context: Context): File {
        return withContext(Dispatchers.IO) {
            val modelDir = File(context.filesDir, "audiocodec_models")
            if (!modelDir.exists()) {
                modelDir.mkdirs()
            }
            
            // 复制主模型文件
            copyAssetToFile(context, "models/refvq_v1_310000_export.pt", File(modelDir, "refvq_v1_310000_export.pt"))
            
            // 复制其他必要的模型文件
            copyAssetToFile(context, "models/mel2lf0_model.ckpt-90000.pt", File(modelDir, "mel2lf0_model.ckpt-90000.pt"))
            copyAssetToFile(context, "models/G3k_600000.pth", File(modelDir, "G3k_600000.pth"))
            copyAssetToFile(context, "models/model.pth", File(modelDir, "model.pth"))
            
            modelDir
        }
    }
    
    /**
     * 从assets复制文件到指定位置
     */
    private fun copyAssetToFile(context: Context, assetPath: String, targetFile: File) {
        try {
            context.assets.open(assetPath).use { inputStream ->
                FileOutputStream(targetFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }
            Timber.d("Copied asset: $assetPath to ${targetFile.absolutePath}")
        } catch (e: Exception) {
            Timber.e(e, "Failed to copy asset: $assetPath")
        }
    }
}
