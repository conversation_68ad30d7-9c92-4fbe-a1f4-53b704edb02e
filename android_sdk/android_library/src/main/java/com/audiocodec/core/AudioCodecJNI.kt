package com.audiocodec.core

import com.audiocodec.model.CodecResult

/**
 * JNI接口类，与C++层进行交互
 */
object AudioCodecJNI {
    
    /**
     * 初始化音频编解码器
     * @param modelPath 模型文件路径
     * @param useCuda 是否使用CUDA
     * @param sampleRate 采样率
     * @param hopSize 跳跃大小
     * @return 是否初始化成功
     */
    external fun initialize(
        modelPath: String,
        useCuda: Boolean,
        sampleRate: Int,
        hopSize: Int
    ): Boolean
    
    /**
     * 编码音频文件
     * @param inputPath 输入音频文件路径
     * @return 编码结果
     */
    external fun encodeAudio(inputPath: String): CodecResult
    
    /**
     * 解码音频数据
     * @param codes 编码数据
     * @param codesS 辅助编码数据
     * @param outputPath 输出文件路径
     * @return 解码结果
     */
    external fun decodeAudio(
        codes: ByteArray,
        codesS: ByteArray,
        outputPath: String
    ): CodecResult
    
    /**
     * 编码音频数据（内存版本）
     * @param audioData 音频数据
     * @param sampleRate 采样率
     * @return 编码结果
     */
    external fun encodeAudioData(
        audioData: FloatArray,
        sampleRate: Int
    ): CodecResult
    
    /**
     * 解码音频数据到内存
     * @param codes 编码数据
     * @param codesS 辅助编码数据
     * @return 解码结果（包含音频数据）
     */
    external fun decodeAudioData(
        codes: ByteArray,
        codesS: ByteArray
    ): CodecResult
    
    /**
     * 获取编解码器信息
     * @return 编解码器信息字符串
     */
    external fun getCodecInfo(): String
    
    /**
     * 设置日志级别
     * @param level 日志级别 (0=VERBOSE, 1=DEBUG, 2=INFO, 3=WARN, 4=ERROR)
     */
    external fun setLogLevel(level: Int)
    
    /**
     * 释放资源
     */
    external fun release()
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    external fun isInitialized(): Boolean
    
    /**
     * 获取支持的音频格式
     * @return 支持的格式数组
     */
    external fun getSupportedFormats(): Array<String>
    
    /**
     * 验证音频文件
     * @param filePath 文件路径
     * @return 是否为有效的音频文件
     */
    external fun validateAudioFile(filePath: String): Boolean
    
    /**
     * 获取音频文件信息
     * @param filePath 文件路径
     * @return 音频信息（JSON格式字符串）
     */
    external fun getAudioInfo(filePath: String): String
}
