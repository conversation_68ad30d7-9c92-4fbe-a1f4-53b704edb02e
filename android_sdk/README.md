# Audio Codec Android SDK

## 项目概述

这是一个基于深度学习的音频编解码Android SDK，将原有的Linux Python实现移植到Android平台。

## 项目结构

```
android_sdk/
├── README.md                    # 项目说明文档
├── docs/                        # 文档目录
│   ├── user_guide.md           # 用户指导手册
│   ├── developer_guide.md      # 开发者文档
│   └── api_reference.md        # API参考文档
├── core/                        # 核心算法实现
│   ├── cpp/                    # C++推理引擎
│   ├── models/                 # 模型文件
│   └── jni/                    # JNI绑定层
├── android_library/            # Android库模块
│   ├── src/                    # 源代码
│   ├── build.gradle           # 构建配置
│   └── proguard-rules.pro     # 混淆规则
├── sample_app/                 # 示例应用
│   ├── src/                    # 示例代码
│   ├── res/                    # 资源文件
│   └── build.gradle           # 构建配置
├── tests/                      # 测试代码
│   ├── unit_tests/            # 单元测试
│   └── integration_tests/     # 集成测试
├── tools/                      # 工具脚本
│   ├── model_converter.py     # 模型转换工具
│   └── build_scripts/         # 构建脚本
├── build.gradle               # 根级构建配置
├── settings.gradle            # 项目设置
└── gradle.properties          # Gradle属性
```

## 核心功能

1. **音频编码** - 将音频文件压缩为高效的编码格式
2. **音频解码** - 将编码数据还原为高质量音频
3. **实时处理** - 支持实时音频流处理
4. **多格式支持** - 支持WAV、MP3等多种音频格式

## 技术特点

- 基于PyTorch模型的深度学习算法
- 高压缩比，低延迟
- 优化的Android性能
- 简洁的API接口
- 完整的错误处理

## 系统要求

- Android API Level 21+ (Android 5.0+)
- ARM64架构支持
- 最小内存要求：512MB
- 推荐内存：1GB+

## 快速开始

详细的使用说明请参考 [用户指导手册](docs/user_guide.md)

## 开发指南

开发者文档请参考 [开发者指南](docs/developer_guide.md)

## 许可证

本项目采用商业许可证，详情请联系开发团队。
