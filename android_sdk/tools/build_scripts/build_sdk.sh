#!/bin/bash

# AudioCodec SDK 构建脚本
# 用于自动化构建整个Android SDK项目

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        log_error "文件不存在: $1"
        exit 1
    fi
}

# 检查目录是否存在
check_directory() {
    if [ ! -d "$1" ]; then
        log_error "目录不存在: $1"
        exit 1
    fi
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
ORIGINAL_PROJECT_ROOT="$(cd "$PROJECT_ROOT/.." && pwd)"

log_info "项目根目录: $PROJECT_ROOT"
log_info "原始项目目录: $ORIGINAL_PROJECT_ROOT"

# 检查必要的工具
log_info "检查构建工具..."
check_command "python3"
check_command "gradle"
check_command "adb"

# 检查Android SDK
if [ -z "$ANDROID_HOME" ]; then
    log_error "ANDROID_HOME 环境变量未设置"
    exit 1
fi

log_info "Android SDK: $ANDROID_HOME"

# 检查原始项目文件
log_info "检查原始项目文件..."
check_file "$ORIGINAL_PROJECT_ROOT/refvq_v1_310000_export.pt"
check_directory "$ORIGINAL_PROJECT_ROOT/dspgan"

# 设置构建参数
BUILD_TYPE=${1:-debug}  # debug 或 release
CLEAN_BUILD=${2:-false}  # 是否清理构建

log_info "构建类型: $BUILD_TYPE"
log_info "清理构建: $CLEAN_BUILD"

# 步骤1: 转换模型
log_info "步骤1: 转换模型文件..."
cd "$PROJECT_ROOT"

MODELS_DIR="$PROJECT_ROOT/core/models"
mkdir -p "$MODELS_DIR"

python3 tools/model_converter.py \
    --input "$ORIGINAL_PROJECT_ROOT" \
    --output "$MODELS_DIR"

if [ $? -eq 0 ]; then
    log_success "模型转换完成"
else
    log_error "模型转换失败"
    exit 1
fi

# 步骤2: 复制模型到assets
log_info "步骤2: 复制模型到Android assets..."
ASSETS_DIR="$PROJECT_ROOT/android_library/src/main/assets/models"
mkdir -p "$ASSETS_DIR"

cp "$MODELS_DIR"/*.pt "$ASSETS_DIR/" 2>/dev/null || true
cp "$MODELS_DIR"/*.pth "$ASSETS_DIR/" 2>/dev/null || true

if [ -f "$MODELS_DIR/android_assets/models/model_info.json" ]; then
    cp "$MODELS_DIR/android_assets/models/model_info.json" "$ASSETS_DIR/"
fi

log_success "模型文件复制完成"

# 步骤3: 下载和配置PyTorch Mobile
log_info "步骤3: 配置PyTorch Mobile..."
PYTORCH_DIR="$PROJECT_ROOT/android_library/src/main/cpp/pytorch"
mkdir -p "$PYTORCH_DIR"

# 检查是否已经下载了PyTorch Mobile
if [ ! -d "$PYTORCH_DIR/lib" ]; then
    log_info "下载PyTorch Mobile库..."
    
    # 这里应该下载实际的PyTorch Mobile库
    # 由于这是示例，我们创建占位符目录
    mkdir -p "$PYTORCH_DIR/lib/arm64-v8a"
    mkdir -p "$PYTORCH_DIR/lib/armeabi-v7a"
    mkdir -p "$PYTORCH_DIR/include"
    
    log_warning "PyTorch Mobile库需要手动下载和配置"
    log_warning "请从 https://pytorch.org/mobile/android/ 下载相应的库文件"
fi

# 步骤4: 清理构建（如果需要）
if [ "$CLEAN_BUILD" = "true" ]; then
    log_info "步骤4: 清理之前的构建..."
    cd "$PROJECT_ROOT"
    ./gradlew clean
    log_success "构建清理完成"
fi

# 步骤5: 构建Android库
log_info "步骤5: 构建Android库..."
cd "$PROJECT_ROOT"

if [ "$BUILD_TYPE" = "release" ]; then
    ./gradlew :android_library:assembleRelease
    LIBRARY_OUTPUT="$PROJECT_ROOT/android_library/build/outputs/aar/android_library-release.aar"
else
    ./gradlew :android_library:assembleDebug
    LIBRARY_OUTPUT="$PROJECT_ROOT/android_library/build/outputs/aar/android_library-debug.aar"
fi

if [ $? -eq 0 ]; then
    log_success "Android库构建完成: $LIBRARY_OUTPUT"
else
    log_error "Android库构建失败"
    exit 1
fi

# 步骤6: 构建示例应用
log_info "步骤6: 构建示例应用..."

if [ "$BUILD_TYPE" = "release" ]; then
    ./gradlew :sample_app:assembleRelease
    APP_OUTPUT="$PROJECT_ROOT/sample_app/build/outputs/apk/release/sample_app-release.apk"
else
    ./gradlew :sample_app:assembleDebug
    APP_OUTPUT="$PROJECT_ROOT/sample_app/build/outputs/apk/debug/sample_app-debug.apk"
fi

if [ $? -eq 0 ]; then
    log_success "示例应用构建完成: $APP_OUTPUT"
else
    log_error "示例应用构建失败"
    exit 1
fi

# 步骤7: 运行测试
log_info "步骤7: 运行单元测试..."
./gradlew :android_library:testDebugUnitTest

if [ $? -eq 0 ]; then
    log_success "单元测试通过"
else
    log_warning "单元测试失败，但继续构建"
fi

# 步骤8: 生成文档
log_info "步骤8: 生成API文档..."
./gradlew :android_library:dokkaHtml 2>/dev/null || log_warning "文档生成失败（可能未安装dokka插件）"

# 步骤9: 创建发布包
log_info "步骤9: 创建发布包..."
RELEASE_DIR="$PROJECT_ROOT/release"
mkdir -p "$RELEASE_DIR"

# 复制构建产物
cp "$LIBRARY_OUTPUT" "$RELEASE_DIR/"
cp "$APP_OUTPUT" "$RELEASE_DIR/"

# 复制文档
cp -r "$PROJECT_ROOT/docs" "$RELEASE_DIR/" 2>/dev/null || true

# 创建版本信息
VERSION=$(date +"%Y%m%d_%H%M%S")
echo "AudioCodec SDK" > "$RELEASE_DIR/VERSION.txt"
echo "Version: $VERSION" >> "$RELEASE_DIR/VERSION.txt"
echo "Build Type: $BUILD_TYPE" >> "$RELEASE_DIR/VERSION.txt"
echo "Build Date: $(date)" >> "$RELEASE_DIR/VERSION.txt"

# 创建安装脚本
cat > "$RELEASE_DIR/install.sh" << 'EOF'
#!/bin/bash
echo "AudioCodec SDK 安装脚本"
echo "========================"

# 检查设备连接
if ! adb devices | grep -q "device$"; then
    echo "错误: 没有检测到Android设备"
    echo "请确保设备已连接并启用USB调试"
    exit 1
fi

# 安装示例应用
echo "正在安装示例应用..."
adb install -r sample_app-*.apk

if [ $? -eq 0 ]; then
    echo "示例应用安装成功"
    echo "可以在设备上找到 'AudioCodec SDK 示例' 应用"
else
    echo "示例应用安装失败"
    exit 1
fi

echo "安装完成！"
EOF

chmod +x "$RELEASE_DIR/install.sh"

log_success "发布包创建完成: $RELEASE_DIR"

# 步骤10: 显示构建摘要
log_info "构建摘要"
echo "=================================="
echo "构建类型: $BUILD_TYPE"
echo "Android库: $(basename "$LIBRARY_OUTPUT")"
echo "示例应用: $(basename "$APP_OUTPUT")"
echo "发布目录: $RELEASE_DIR"
echo "=================================="

# 可选: 自动安装到设备
if [ "$3" = "install" ]; then
    log_info "自动安装到设备..."
    if adb devices | grep -q "device$"; then
        adb install -r "$APP_OUTPUT"
        log_success "应用已安装到设备"
    else
        log_warning "没有检测到设备，跳过自动安装"
    fi
fi

log_success "构建完成！"
