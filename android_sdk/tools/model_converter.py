#!/usr/bin/env python3
"""
AudioCodec 模型转换工具

将原始的PyTorch模型转换为适用于Android的格式：
1. 转换为TorchScript格式
2. 优化模型结构
3. 量化模型（可选）
4. 验证转换结果
"""

import os
import sys
import argparse
import torch
import torch.jit
import numpy as np
from pathlib import Path
import logging

# 添加原始项目路径到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from singlecodec_enc import encode
    from singlecodec_dec import decode
    from dspgan.mel2lf0.gen_pitch2 import pitch_synthesis
    from dspgan.nhv.resyn2 import generate_audio
    from config import preConfiged16K
except ImportError as e:
    print(f"Error importing original modules: {e}")
    print("Please ensure the original project is in the parent directory")
    sys.exit(1)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelConverter:
    """模型转换器类"""
    
    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def convert_main_codec_model(self):
        """转换主编解码模型"""
        logger.info("Converting main codec model...")
        
        input_path = self.input_dir / "refvq_v1_310000_export.pt"
        output_path = self.output_dir / "refvq_v1_310000_export.pt"
        
        if not input_path.exists():
            logger.error(f"Main codec model not found: {input_path}")
            return False
            
        try:
            # 加载原始模型
            model = torch.jit.load(str(input_path), map_location='cpu')
            model.eval()
            
            # 优化模型
            optimized_model = torch.jit.optimize_for_inference(model)
            
            # 保存优化后的模型
            torch.jit.save(optimized_model, str(output_path))
            
            logger.info(f"Main codec model converted: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to convert main codec model: {e}")
            return False
    
    def convert_mel2lf0_model(self):
        """转换mel2lf0模型"""
        logger.info("Converting mel2lf0 model...")
        
        # 查找mel2lf0模型文件
        mel2lf0_dir = self.input_dir / "dspgan" / "mel2lf0" / "logdir" / "16k"
        model_files = list(mel2lf0_dir.glob("model.ckpt-*.pt"))
        
        if not model_files:
            logger.error(f"Mel2lf0 model not found in: {mel2lf0_dir}")
            return False
            
        # 选择最新的模型文件
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        output_path = self.output_dir / "mel2lf0_model.ckpt-90000.pt"
        
        try:
            # 复制模型文件（mel2lf0模型通常已经是TorchScript格式）
            import shutil
            shutil.copy2(str(latest_model), str(output_path))
            
            logger.info(f"Mel2lf0 model converted: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to convert mel2lf0 model: {e}")
            return False
    
    def convert_synthesis_models(self):
        """转换音频合成模型"""
        logger.info("Converting synthesis models...")
        
        # NHV模型文件
        nhv_dir = self.input_dir / "dspgan" / "nhv" / "ckpts"
        model_files = [
            ("G3k_600000.pth", "G3k_600000.pth"),
            ("model.pth", "model.pth")
        ]
        
        success = True
        for input_name, output_name in model_files:
            input_path = nhv_dir / input_name
            output_path = self.output_dir / output_name
            
            if not input_path.exists():
                logger.warning(f"Synthesis model not found: {input_path}")
                continue
                
            try:
                # 复制模型文件
                import shutil
                shutil.copy2(str(input_path), str(output_path))
                logger.info(f"Synthesis model converted: {output_path}")
                
            except Exception as e:
                logger.error(f"Failed to convert synthesis model {input_name}: {e}")
                success = False
                
        return success
    
    def create_model_wrapper(self):
        """创建模型包装器，用于统一接口"""
        logger.info("Creating model wrapper...")
        
        wrapper_code = '''
import torch
import torch.nn as nn

class AudioCodecWrapper(nn.Module):
    """AudioCodec模型包装器，提供统一的编解码接口"""
    
    def __init__(self, main_model_path, mel2lf0_model_path, synthesis_model_paths):
        super().__init__()
        
        # 加载主编解码模型
        self.main_model = torch.jit.load(main_model_path, map_location='cpu')
        self.main_model.eval()
        
        # 加载mel2lf0模型
        self.mel2lf0_model = torch.jit.load(mel2lf0_model_path, map_location='cpu')
        self.mel2lf0_model.eval()
        
        # 加载合成模型（这里需要根据实际模型结构调整）
        self.synthesis_models = {}
        for name, path in synthesis_model_paths.items():
            self.synthesis_models[name] = torch.load(path, map_location='cpu')
    
    def encode(self, mel_input, prompt):
        """编码mel频谱"""
        with torch.no_grad():
            sampled, _, codes, sampled_s, _, codes_s = self.main_model.encode(mel_input, prompt)
            return codes, codes_s
    
    def decode(self, codes, codes_s):
        """解码mel频谱"""
        with torch.no_grad():
            mel_output = self.main_model.decode(codes, codes_s)
            return mel_output
    
    def generate_pitch(self, mel_input):
        """生成音高信息"""
        with torch.no_grad():
            pitch = self.mel2lf0_model(mel_input)
            return pitch
    
    def synthesize_audio(self, mel_input, pitch_input):
        """合成音频"""
        # 这里需要根据实际的合成模型接口调整
        with torch.no_grad():
            # 使用合成模型生成音频
            audio = self.synthesis_models['nhv'](mel_input, pitch_input)
            return audio

# 创建包装器实例的工厂函数
def create_wrapper(model_dir):
    main_model_path = f"{model_dir}/refvq_v1_310000_export.pt"
    mel2lf0_model_path = f"{model_dir}/mel2lf0_model.ckpt-90000.pt"
    synthesis_model_paths = {
        'g3k': f"{model_dir}/G3k_600000.pth",
        'nhv': f"{model_dir}/model.pth"
    }
    
    return AudioCodecWrapper(main_model_path, mel2lf0_model_path, synthesis_model_paths)
'''
        
        wrapper_path = self.output_dir / "model_wrapper.py"
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
            
        logger.info(f"Model wrapper created: {wrapper_path}")
        return True
    
    def validate_models(self):
        """验证转换后的模型"""
        logger.info("Validating converted models...")
        
        required_files = [
            "refvq_v1_310000_export.pt",
            "mel2lf0_model.ckpt-90000.pt",
            "G3k_600000.pth",
            "model.pth"
        ]
        
        missing_files = []
        for filename in required_files:
            file_path = self.output_dir / filename
            if not file_path.exists():
                missing_files.append(filename)
            else:
                # 检查文件大小
                file_size = file_path.stat().st_size
                if file_size == 0:
                    missing_files.append(f"{filename} (empty)")
                else:
                    logger.info(f"✓ {filename}: {file_size / (1024*1024):.1f} MB")
        
        if missing_files:
            logger.error(f"Missing or invalid model files: {missing_files}")
            return False
        
        # 尝试加载主模型进行基本验证
        try:
            main_model_path = self.output_dir / "refvq_v1_310000_export.pt"
            model = torch.jit.load(str(main_model_path), map_location='cpu')
            model.eval()
            
            # 创建测试输入
            test_input = torch.randn(1, 80, 100)  # 假设的mel频谱输入
            test_prompt = torch.randn(1, 80, 600)  # 假设的prompt输入
            
            # 测试编码
            with torch.no_grad():
                result = model.encode(test_input, test_prompt)
                logger.info("✓ Main model encoding test passed")
            
        except Exception as e:
            logger.error(f"Main model validation failed: {e}")
            return False
        
        logger.info("All models validated successfully")
        return True
    
    def create_android_assets(self):
        """创建Android assets目录结构"""
        logger.info("Creating Android assets structure...")
        
        assets_dir = self.output_dir / "android_assets" / "models"
        assets_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制模型文件到assets目录
        model_files = [
            "refvq_v1_310000_export.pt",
            "mel2lf0_model.ckpt-90000.pt",
            "G3k_600000.pth",
            "model.pth"
        ]
        
        import shutil
        for filename in model_files:
            src_path = self.output_dir / filename
            dst_path = assets_dir / filename
            
            if src_path.exists():
                shutil.copy2(str(src_path), str(dst_path))
                logger.info(f"Copied to assets: {filename}")
        
        # 创建模型信息文件
        model_info = {
            "version": "1.0.0",
            "models": {
                "main_codec": {
                    "file": "refvq_v1_310000_export.pt",
                    "type": "torchscript",
                    "description": "Main audio codec model"
                },
                "mel2lf0": {
                    "file": "mel2lf0_model.ckpt-90000.pt",
                    "type": "torchscript",
                    "description": "Mel to F0 conversion model"
                },
                "synthesis_g3k": {
                    "file": "G3k_600000.pth",
                    "type": "pytorch",
                    "description": "Audio synthesis model (3kHz)"
                },
                "synthesis_nhv": {
                    "file": "model.pth",
                    "type": "pytorch",
                    "description": "Neural harmonic-plus-noise vocoder"
                }
            }
        }
        
        import json
        info_path = assets_dir / "model_info.json"
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Android assets created in: {assets_dir}")
        return True
    
    def convert_all(self):
        """执行完整的模型转换流程"""
        logger.info("Starting model conversion...")
        
        steps = [
            ("Converting main codec model", self.convert_main_codec_model),
            ("Converting mel2lf0 model", self.convert_mel2lf0_model),
            ("Converting synthesis models", self.convert_synthesis_models),
            ("Creating model wrapper", self.create_model_wrapper),
            ("Validating models", self.validate_models),
            ("Creating Android assets", self.create_android_assets)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"Step: {step_name}")
            if not step_func():
                logger.error(f"Failed at step: {step_name}")
                return False
        
        logger.info("Model conversion completed successfully!")
        return True

def main():
    parser = argparse.ArgumentParser(description="AudioCodec Model Converter")
    parser.add_argument("--input", "-i", required=True, 
                       help="Input directory containing original models")
    parser.add_argument("--output", "-o", required=True,
                       help="Output directory for converted models")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate existing converted models")
    
    args = parser.parse_args()
    
    converter = ModelConverter(args.input, args.output)
    
    if args.validate_only:
        success = converter.validate_models()
    else:
        success = converter.convert_all()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
