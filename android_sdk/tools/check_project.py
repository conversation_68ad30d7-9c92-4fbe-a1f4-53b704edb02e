#!/usr/bin/env python3
"""
AudioCodec Android SDK 项目完整性检查工具

检查项目结构、文件完整性和配置正确性
"""

import os
import sys
from pathlib import Path
import json

class ProjectChecker:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.errors = []
        self.warnings = []
        self.success_count = 0
        self.total_checks = 0
    
    def check_file(self, file_path, description=""):
        """检查文件是否存在"""
        self.total_checks += 1
        full_path = self.project_root / file_path
        
        if full_path.exists():
            print(f"✓ {description or file_path}")
            self.success_count += 1
            return True
        else:
            error_msg = f"✗ 缺失文件: {file_path}"
            if description:
                error_msg += f" ({description})"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_directory(self, dir_path, description=""):
        """检查目录是否存在"""
        self.total_checks += 1
        full_path = self.project_root / dir_path
        
        if full_path.exists() and full_path.is_dir():
            print(f"✓ {description or dir_path}")
            self.success_count += 1
            return True
        else:
            error_msg = f"✗ 缺失目录: {dir_path}"
            if description:
                error_msg += f" ({description})"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_file_content(self, file_path, expected_content, description=""):
        """检查文件内容"""
        self.total_checks += 1
        full_path = self.project_root / file_path
        
        if not full_path.exists():
            error_msg = f"✗ 文件不存在: {file_path}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if expected_content in content:
                print(f"✓ {description or f'{file_path} 内容检查'}")
                self.success_count += 1
                return True
            else:
                warning_msg = f"⚠ {file_path} 可能缺少预期内容"
                if description:
                    warning_msg += f" ({description})"
                print(warning_msg)
                self.warnings.append(warning_msg)
                return False
                
        except Exception as e:
            error_msg = f"✗ 读取文件失败: {file_path} - {e}"
            print(error_msg)
            self.errors.append(error_msg)
            return False
    
    def check_gradle_files(self):
        """检查Gradle配置文件"""
        print("\n=== 检查Gradle配置 ===")
        
        # 根级配置文件
        self.check_file("build.gradle", "根级构建配置")
        self.check_file("settings.gradle", "项目设置")
        self.check_file("gradle.properties", "Gradle属性")
        
        # 库模块配置
        self.check_file("android_library/build.gradle", "库模块构建配置")
        self.check_file("android_library/proguard-rules.pro", "混淆规则")
        
        # 示例应用配置
        self.check_file("sample_app/build.gradle", "示例应用构建配置")
        
        # 检查关键配置内容
        self.check_file_content(
            "android_library/build.gradle",
            "com.android.library",
            "库模块插件配置"
        )
        
        self.check_file_content(
            "sample_app/build.gradle",
            "com.android.application",
            "应用模块插件配置"
        )
    
    def check_source_code(self):
        """检查源代码文件"""
        print("\n=== 检查源代码 ===")
        
        # Kotlin/Java源码
        kotlin_files = [
            "android_library/src/main/java/com/audiocodec/AudioCodecSDK.kt",
            "android_library/src/main/java/com/audiocodec/model/CodecConfig.kt",
            "android_library/src/main/java/com/audiocodec/model/CodecResult.kt",
            "android_library/src/main/java/com/audiocodec/callback/CodecCallback.kt",
            "android_library/src/main/java/com/audiocodec/core/AudioCodecJNI.kt",
        ]
        
        for file_path in kotlin_files:
            self.check_file(file_path, f"Kotlin源码: {Path(file_path).name}")
        
        # C++源码
        cpp_files = [
            "android_library/src/main/cpp/audiocodec_jni.cpp",
            "android_library/src/main/cpp/codec_engine.cpp",
            "android_library/src/main/cpp/codec_engine_impl.cpp",
            "android_library/src/main/cpp/audio_processor.cpp",
            "android_library/src/main/cpp/model_manager.cpp",
            "android_library/src/main/cpp/utils.cpp",
        ]
        
        for file_path in cpp_files:
            self.check_file(file_path, f"C++源码: {Path(file_path).name}")
        
        # 头文件
        header_files = [
            "android_library/src/main/cpp/include/audiocodec_jni.h",
            "android_library/src/main/cpp/include/codec_engine.h",
        ]
        
        for file_path in header_files:
            self.check_file(file_path, f"C++头文件: {Path(file_path).name}")
        
        # CMakeLists.txt
        self.check_file("android_library/src/main/cpp/CMakeLists.txt", "CMake配置")
    
    def check_sample_app(self):
        """检查示例应用"""
        print("\n=== 检查示例应用 ===")
        
        # 示例应用源码
        sample_files = [
            "sample_app/src/main/java/com/audiocodec/sample/MainActivity.kt",
            "sample_app/src/main/java/com/audiocodec/sample/CodecTestActivity.kt",
            "sample_app/src/main/java/com/audiocodec/sample/SampleApplication.kt",
        ]
        
        for file_path in sample_files:
            self.check_file(file_path, f"示例应用: {Path(file_path).name}")
        
        # 布局文件
        layout_files = [
            "sample_app/src/main/res/layout/activity_main.xml",
            "sample_app/src/main/res/layout/activity_codec_test.xml",
        ]
        
        for file_path in layout_files:
            self.check_file(file_path, f"布局文件: {Path(file_path).name}")
        
        # 资源文件
        resource_files = [
            "sample_app/src/main/res/values/strings.xml",
            "sample_app/src/main/res/values/colors.xml",
            "sample_app/src/main/res/values/themes.xml",
        ]
        
        for file_path in resource_files:
            self.check_file(file_path, f"资源文件: {Path(file_path).name}")
        
        # Manifest文件
        self.check_file("sample_app/src/main/AndroidManifest.xml", "应用Manifest")
        self.check_file("android_library/src/main/AndroidManifest.xml", "库Manifest")
    
    def check_documentation(self):
        """检查文档"""
        print("\n=== 检查文档 ===")
        
        doc_files = [
            "README.md",
            "IMPLEMENTATION_SUMMARY.md",
            "docs/user_guide.md",
            "docs/developer_guide.md",
            "docs/api_reference.md",
        ]
        
        for file_path in doc_files:
            self.check_file(file_path, f"文档: {Path(file_path).name}")
    
    def check_tools(self):
        """检查工具脚本"""
        print("\n=== 检查工具脚本 ===")
        
        tool_files = [
            "tools/model_converter.py",
            "tools/build_scripts/build_sdk.sh",
        ]
        
        for file_path in tool_files:
            self.check_file(file_path, f"工具脚本: {Path(file_path).name}")
    
    def check_project_structure(self):
        """检查项目目录结构"""
        print("\n=== 检查项目结构 ===")
        
        directories = [
            "android_library",
            "android_library/src",
            "android_library/src/main",
            "android_library/src/main/java",
            "android_library/src/main/java/com",
            "android_library/src/main/java/com/audiocodec",
            "android_library/src/main/cpp",
            "android_library/src/main/cpp/include",
            "sample_app",
            "sample_app/src",
            "sample_app/src/main",
            "sample_app/src/main/java",
            "sample_app/src/main/res",
            "docs",
            "tools",
            "tools/build_scripts",
            "tests",
            "tests/unit_tests",
            "tests/integration_tests",
            "core",
            "core/cpp",
            "core/models",
            "core/jni",
        ]
        
        for dir_path in directories:
            self.check_directory(dir_path)
    
    def check_all(self):
        """执行所有检查"""
        print("AudioCodec Android SDK 项目完整性检查")
        print("=" * 50)
        
        self.check_project_structure()
        self.check_gradle_files()
        self.check_source_code()
        self.check_sample_app()
        self.check_documentation()
        self.check_tools()
        
        # 输出检查结果
        print("\n" + "=" * 50)
        print("检查结果汇总:")
        print(f"总检查项: {self.total_checks}")
        print(f"通过: {self.success_count}")
        print(f"错误: {len(self.errors)}")
        print(f"警告: {len(self.warnings)}")
        
        if self.errors:
            print(f"\n错误列表:")
            for error in self.errors:
                print(f"  {error}")
        
        if self.warnings:
            print(f"\n警告列表:")
            for warning in self.warnings:
                print(f"  {warning}")
        
        success_rate = (self.success_count / self.total_checks) * 100 if self.total_checks > 0 else 0
        print(f"\n完整性: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✓ 项目结构完整，可以进行构建")
            return True
        elif success_rate >= 70:
            print("⚠ 项目基本完整，但有一些问题需要解决")
            return False
        else:
            print("✗ 项目结构不完整，需要补充缺失的文件")
            return False

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        # 默认使用脚本所在目录的上级目录
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
    
    checker = ProjectChecker(project_root)
    success = checker.check_all()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
