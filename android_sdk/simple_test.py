#!/usr/bin/env python3
"""
简化的项目测试脚本
只检查基本的项目结构和语法问题
"""

import os
import sys
from pathlib import Path

def check_basic_structure():
    """检查基本项目结构"""
    print("检查基本项目结构...")
    
    required_files = [
        "build.gradle",
        "settings.gradle",
        "android_library/build.gradle",
        "sample_app/build.gradle"
    ]
    
    missing = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing:
        print("✗ 缺少文件:")
        for f in missing:
            print(f"  - {f}")
        return False
    
    return True

def check_kotlin_files():
    """检查Kotlin文件语法"""
    print("\n检查Kotlin文件...")
    
    kotlin_files = [
        "android_library/src/main/java/com/audiocodec/AudioCodecSDK.kt",
        "android_library/src/main/java/com/audiocodec/model/CodecConfig.kt",
        "sample_app/src/main/java/com/audiocodec/sample/MainActivity.kt"
    ]
    
    for file_path in kotlin_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            return False
    
    return True

def check_cpp_files():
    """检查C++文件"""
    print("\n检查C++文件...")
    
    cpp_files = [
        "android_library/src/main/cpp/CMakeLists.txt",
        "android_library/src/main/cpp/audiocodec_jni.cpp",
        "android_library/src/main/cpp/codec_engine.cpp"
    ]
    
    for file_path in cpp_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            return False
    
    return True

def check_resources():
    """检查资源文件"""
    print("\n检查资源文件...")
    
    resource_files = [
        "sample_app/src/main/res/layout/activity_main.xml",
        "sample_app/src/main/res/values/strings.xml",
        "sample_app/src/main/AndroidManifest.xml"
    ]
    
    for file_path in resource_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            return False
    
    return True

def check_gradle_wrapper():
    """检查Gradle Wrapper"""
    print("\n检查Gradle Wrapper...")
    
    wrapper_files = [
        "gradlew",
        "gradle/wrapper/gradle-wrapper.properties"
    ]
    
    for file_path in wrapper_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path}")
            return False
    
    return True

def main():
    print("AudioCodec Android SDK 简化测试")
    print("=" * 40)
    
    if not Path("settings.gradle").exists():
        print("✗ 请在项目根目录运行此脚本")
        return False
    
    tests = [
        ("基本结构", check_basic_structure),
        ("Kotlin文件", check_kotlin_files),
        ("C++文件", check_cpp_files),
        ("资源文件", check_resources),
        ("Gradle Wrapper", check_gradle_wrapper)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {name} 检查失败")
        except Exception as e:
            print(f"❌ {name} 检查出错: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 基本结构检查通过！")
        print("\n下一步:")
        print("1. 安装Java 8+")
        print("2. 安装Android Studio")
        print("3. 下载PyTorch Mobile库")
        print("4. 运行 ./gradlew build")
        return True
    else:
        print("⚠️ 部分检查失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
