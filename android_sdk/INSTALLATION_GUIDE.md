# AudioCodec Android SDK 安装和使用指南

## 🔧 环境要求

### 必需软件

1. **Java Development Kit (JDK)**
   - 版本：JDK 8 或更高版本（推荐 JDK 11）
   - 下载地址：https://adoptium.net/

2. **Android Studio**
   - 版本：Android Studio 4.0 或更高版本
   - 下载地址：https://developer.android.com/studio

3. **Android SDK**
   - API Level 21 (Android 5.0) 或更高版本
   - 通过Android Studio的SDK Manager安装

4. **Android NDK**
   - 版本：NDK r21 或更高版本
   - 通过Android Studio的SDK Manager安装

5. **Python 3**
   - 版本：Python 3.7 或更高版本
   - 用于模型转换工具

### 可选软件

1. **Git** - 用于版本控制
2. **ADB** - 用于设备调试（通常随Android SDK安装）

## 📦 依赖库准备

### 1. PyTorch Mobile 库

由于文件大小限制，PyTorch Mobile库需要手动下载：

```bash
# 创建目录
mkdir -p android_library/src/main/cpp/pytorch/lib/arm64-v8a
mkdir -p android_library/src/main/cpp/pytorch/lib/armeabi-v7a
mkdir -p android_library/src/main/cpp/pytorch/include

# 下载PyTorch Mobile库
# 访问 https://pytorch.org/mobile/android/
# 下载适用于Android的预编译库
```

**需要的库文件：**
- `libpytorch_jni.so` (ARM64和ARMv7版本)
- `libfbjni.so` (ARM64和ARMv7版本)
- PyTorch头文件

### 2. ONNX Runtime (可选)

如果使用ONNX模型：

```bash
# 下载ONNX Runtime for Android
# 访问 https://github.com/microsoft/onnxruntime/releases
```

## 🚀 安装步骤

### 步骤1: 环境检查

```bash
# 检查Java版本
java -version

# 检查Android SDK
echo $ANDROID_HOME

# 检查Python版本
python3 --version
```

### 步骤2: 克隆或下载项目

```bash
# 如果从Git仓库克隆
git clone <repository-url>
cd android_sdk

# 或者直接使用提供的项目文件夹
cd /path/to/android_sdk
```

### 步骤3: 设置环境变量

在 `~/.bashrc` 或 `~/.zshrc` 中添加：

```bash
export ANDROID_HOME=/path/to/android/sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
export JAVA_HOME=/path/to/java
```

然后重新加载：
```bash
source ~/.bashrc  # 或 source ~/.zshrc
```

### 步骤4: 准备模型文件

```bash
# 运行模型转换工具
python3 tools/model_converter.py \
    --input /path/to/original/project \
    --output core/models

# 复制模型到assets目录
cp core/models/*.pt android_library/src/main/assets/models/
cp core/models/*.pth android_library/src/main/assets/models/
```

### 步骤5: 配置依赖库

1. **下载PyTorch Mobile库**
   ```bash
   # 将下载的库文件放置到正确位置
   cp libpytorch_jni.so android_library/src/main/cpp/pytorch/lib/arm64-v8a/
   cp libfbjni.so android_library/src/main/cpp/pytorch/lib/arm64-v8a/
   # 对ARMv7也做同样操作
   ```

2. **配置CMakeLists.txt**
   - 确保库路径正确
   - 检查头文件路径

### 步骤6: 构建项目

```bash
# 运行构建测试
python3 test_build.py

# 如果测试通过，构建完整项目
./gradlew build

# 或者使用提供的构建脚本
./tools/build_scripts/build_sdk.sh debug
```

### 步骤7: 安装示例应用

```bash
# 连接Android设备并启用USB调试
adb devices

# 安装示例应用
adb install sample_app/build/outputs/apk/debug/sample_app-debug.apk
```

## 🔍 故障排除

### 常见问题

1. **Gradle同步失败**
   ```
   解决方案：
   - 检查网络连接
   - 清理Gradle缓存：./gradlew clean
   - 检查Gradle版本兼容性
   ```

2. **NDK编译错误**
   ```
   解决方案：
   - 确保NDK版本正确
   - 检查CMakeLists.txt配置
   - 验证库文件路径
   ```

3. **模型文件缺失**
   ```
   解决方案：
   - 运行模型转换工具
   - 检查assets目录
   - 验证文件权限
   ```

4. **设备安装失败**
   ```
   解决方案：
   - 启用USB调试
   - 检查设备兼容性
   - 清理设备存储空间
   ```

### 调试技巧

1. **查看构建日志**
   ```bash
   ./gradlew build --info --stacktrace
   ```

2. **检查设备日志**
   ```bash
   adb logcat | grep AudioCodec
   ```

3. **验证库文件**
   ```bash
   # 检查库文件架构
   file android_library/src/main/cpp/pytorch/lib/arm64-v8a/libpytorch_jni.so
   ```

## 📱 使用示例应用

### 基本功能测试

1. **启动应用**
   - 在设备上找到"AudioCodec SDK 示例"应用
   - 点击启动

2. **初始化SDK**
   - 点击"初始化 SDK"按钮
   - 等待初始化完成

3. **编解码测试**
   - 点击"编解码测试"
   - 选择音频文件
   - 执行编码和解码操作

### 性能测试

1. **运行性能测试**
   - 点击"性能测试"按钮
   - 观察处理时间和资源使用

2. **查看SDK信息**
   - 点击"SDK 信息"查看详细信息

## 🔧 集成到现有项目

### 1. 添加模块依赖

在项目的 `settings.gradle` 中：
```gradle
include ':audiocodec-sdk'
project(':audiocodec-sdk').projectDir = new File('path/to/android_library')
```

在应用的 `build.gradle` 中：
```gradle
dependencies {
    implementation project(':audiocodec-sdk')
}
```

### 2. 权限配置

在 `AndroidManifest.xml` 中添加：
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 3. 基本使用代码

```kotlin
class MyActivity : AppCompatActivity() {
    private val audioCodecSDK = AudioCodecSDK.getInstance()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        lifecycleScope.launch {
            val success = audioCodecSDK.initialize(this@MyActivity)
            if (success) {
                // SDK初始化成功，可以使用编解码功能
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        audioCodecSDK.release()
    }
}
```

## 📞 技术支持

如果遇到问题：

1. 查看 `docs/` 目录下的详细文档
2. 运行 `python3 tools/check_project.py` 检查项目完整性
3. 查看示例应用的实现代码
4. 检查构建日志和设备日志

## 📝 注意事项

1. **模型文件大小** - 确保设备有足够存储空间
2. **性能考虑** - 在低端设备上可能需要调整参数
3. **权限处理** - 确保应用有必要的权限
4. **网络依赖** - 首次构建需要下载依赖库
5. **版本兼容** - 确保所有工具版本兼容

## 🎯 下一步

成功安装后，建议：

1. 阅读 `docs/user_guide.md` 了解详细使用方法
2. 查看 `docs/api_reference.md` 了解API接口
3. 研究示例应用的源码
4. 根据需求调整配置参数
