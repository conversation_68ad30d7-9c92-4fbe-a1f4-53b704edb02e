#!/usr/bin/env python3
"""
简化的构建测试脚本
检查项目是否可以正常构建
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, 
                              capture_output=True, text=True, timeout=300)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_java():
    """检查Java环境"""
    print("检查Java环境...")
    success, stdout, stderr = run_command("java -version")
    if success:
        print("✓ Java已安装")
        return True
    else:
        print("✗ Java未安装或配置错误")
        print("请安装Java 8或更高版本")
        return False

def check_android_sdk():
    """检查Android SDK"""
    print("检查Android SDK...")
    android_home = os.environ.get('ANDROID_HOME')
    if android_home and Path(android_home).exists():
        print(f"✓ Android SDK: {android_home}")
        return True
    else:
        print("✗ ANDROID_HOME未设置或路径不存在")
        print("请设置ANDROID_HOME环境变量")
        return False

def check_project_structure():
    """检查项目结构"""
    print("检查项目结构...")
    
    required_files = [
        "build.gradle",
        "settings.gradle",
        "android_library/build.gradle",
        "sample_app/build.gradle",
        "android_library/src/main/java/com/audiocodec/AudioCodecSDK.kt",
        "sample_app/src/main/java/com/audiocodec/sample/MainActivity.kt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ 缺少以下文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ 项目结构完整")
        return True

def test_gradle_sync():
    """测试Gradle同步"""
    print("测试Gradle同步...")
    
    # 首先尝试运行gradle tasks来测试基本功能
    success, stdout, stderr = run_command("./gradlew tasks --console=plain")
    
    if success:
        print("✓ Gradle同步成功")
        return True
    else:
        print("✗ Gradle同步失败")
        print("错误信息:")
        print(stderr)
        return False

def test_compile():
    """测试编译"""
    print("测试编译...")
    
    # 尝试编译库模块
    success, stdout, stderr = run_command("./gradlew :android_library:compileDebugKotlin --console=plain")
    
    if success:
        print("✓ 库模块编译成功")
    else:
        print("✗ 库模块编译失败")
        print("错误信息:")
        print(stderr)
        return False
    
    # 尝试编译示例应用
    success, stdout, stderr = run_command("./gradlew :sample_app:compileDebugKotlin --console=plain")
    
    if success:
        print("✓ 示例应用编译成功")
        return True
    else:
        print("✗ 示例应用编译失败")
        print("错误信息:")
        print(stderr)
        return False

def main():
    print("AudioCodec Android SDK 构建测试")
    print("=" * 40)
    
    # 检查当前目录
    if not Path("settings.gradle").exists():
        print("✗ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 执行各项检查
    checks = [
        ("Java环境", check_java),
        ("Android SDK", check_android_sdk),
        ("项目结构", check_project_structure),
        ("Gradle同步", test_gradle_sync),
        ("编译测试", test_compile)
    ]
    
    passed = 0
    total = len(checks)
    
    for name, check_func in checks:
        print(f"\n--- {name} ---")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {name} 检查失败")
        except Exception as e:
            print(f"❌ {name} 检查出错: {e}")
    
    print(f"\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常构建")
        return True
    else:
        print("⚠️  部分测试失败，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
