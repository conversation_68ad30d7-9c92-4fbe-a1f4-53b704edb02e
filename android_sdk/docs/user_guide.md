# AudioCodec SDK 用户指导手册

## 概述

AudioCodec SDK 是一个基于深度学习的音频编解码库，专为Android平台设计。它提供了高质量的音频压缩和重建功能，适用于各种音频处理应用。

## 系统要求

### 最低要求
- Android 5.0 (API Level 21) 或更高版本
- ARM64 或 ARMv7 架构
- 512MB 可用内存
- 100MB 可用存储空间

### 推荐配置
- Android 8.0 (API Level 26) 或更高版本
- ARM64 架构
- 1GB 或更多可用内存
- 500MB 或更多可用存储空间

## 安装指南

### 1. 添加依赖

在您的 `build.gradle` 文件中添加以下依赖：

```gradle
dependencies {
    implementation project(':audiocodec-sdk')
    // 或者使用 AAR 文件
    // implementation files('libs/audiocodec-sdk-1.0.0.aar')
}
```

### 2. 权限配置

在 `AndroidManifest.xml` 中添加必要的权限：

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

### 3. 混淆配置

如果您的应用使用了代码混淆，请在 `proguard-rules.pro` 中添加：

```proguard
-keep class com.audiocodec.** { *; }
-keep class com.audiocodec.core.AudioCodecJNI { *; }
-keepclasseswithmembernames class * {
    native <methods>;
}
```

## 快速开始

### 1. 初始化SDK

```kotlin
import com.audiocodec.AudioCodecSDK
import com.audiocodec.model.CodecConfig

class MainActivity : AppCompatActivity() {
    private val audioCodecSDK = AudioCodecSDK.getInstance()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化SDK
        lifecycleScope.launch {
            val config = CodecConfig.create16kConfig()
            val success = audioCodecSDK.initialize(this@MainActivity, config)
            
            if (success) {
                // SDK初始化成功
                Log.i("AudioCodec", "SDK initialized successfully")
            } else {
                // 初始化失败
                Log.e("AudioCodec", "Failed to initialize SDK")
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 释放SDK资源
        audioCodecSDK.release()
    }
}
```

### 2. 编码音频

```kotlin
import com.audiocodec.callback.SimpleCodecCallback

// 编码音频文件
audioCodecSDK.encodeAudio(
    inputPath = "/path/to/input/audio.wav",
    callback = SimpleCodecCallback(
        onSuccessCallback = { result ->
            // 编码成功
            val codes = result.codes
            val codesS = result.codesS
            val compressedSize = result.compressedSize
            Log.i("AudioCodec", "Encoding successful, compressed size: $compressedSize bits")
        },
        onErrorCallback = { error ->
            // 编码失败
            Log.e("AudioCodec", "Encoding failed: $error")
        }
    )
)
```

### 3. 解码音频

```kotlin
// 解码音频数据
audioCodecSDK.decodeAudio(
    codes = encodedCodes,
    codesS = encodedCodesS,
    outputPath = "/path/to/output/audio.wav",
    callback = SimpleCodecCallback(
        onSuccessCallback = { result ->
            // 解码成功
            val outputPath = result.outputPath
            val duration = result.audioDurationSec
            Log.i("AudioCodec", "Decoding successful, output: $outputPath, duration: ${duration}s")
        },
        onErrorCallback = { error ->
            // 解码失败
            Log.e("AudioCodec", "Decoding failed: $error")
        }
    )
)
```

## 配置选项

### CodecConfig 参数说明

```kotlin
val config = CodecConfig(
    useCuda = false,           // 是否使用CUDA（Android上通常为false）
    sampleRate = 16000,        // 采样率（Hz）
    hopSize = 200,             // 跳跃大小
    winSize = 800,             // 窗口大小
    nFft = 1024,               // FFT大小
    fMin = 0,                  // 最小频率（Hz）
    fMax = 8000,               // 最大频率（Hz）
    nMels = 80,                // Mel频谱通道数
    minDb = -115f,             // 最小dB值
    refDb = 20f,               // 参考dB值
    maxAbsValue = 1.0f         // 最大绝对值
)
```

### 预设配置

SDK提供了几种预设配置：

```kotlin
// 16kHz配置（推荐用于语音）
val config16k = CodecConfig.create16kConfig()

// 48kHz配置（推荐用于音乐）
val config48k = CodecConfig.create48kConfig()
```

## 支持的音频格式

### 输入格式
- WAV (PCM)
- MP3
- M4A
- AAC
- FLAC

### 输出格式
- WAV (PCM 16-bit)

## 性能优化建议

### 1. 内存管理
- 及时释放不需要的音频数据
- 避免同时处理多个大文件
- 使用适当的采样率和位深度

### 2. 处理策略
- 对于长音频文件，考虑分段处理
- 在后台线程中执行编解码操作
- 使用进度回调来更新UI

### 3. 缓存策略
- 缓存常用的编码结果
- 使用合适的缓存大小限制

## 错误处理

### 常见错误及解决方案

1. **SDK未初始化**
   - 确保在使用前调用 `initialize()` 方法
   - 检查初始化是否成功

2. **文件不存在或无法访问**
   - 检查文件路径是否正确
   - 确保应用有相应的文件访问权限

3. **不支持的音频格式**
   - 检查输入文件格式是否在支持列表中
   - 考虑先转换为支持的格式

4. **内存不足**
   - 减少同时处理的文件数量
   - 使用较低的采样率或较短的音频片段

5. **编解码失败**
   - 检查音频文件是否损坏
   - 确保有足够的存储空间
   - 检查设备性能是否满足要求

## 示例应用

SDK包含一个完整的示例应用，展示了所有主要功能的使用方法。您可以在 `sample_app` 目录中找到源代码。

### 运行示例应用

1. 打开Android Studio
2. 导入整个 `android_sdk` 项目
3. 选择 `sample_app` 模块
4. 点击运行按钮

### 示例功能

- SDK初始化和释放
- 音频文件选择
- 实时编解码
- 性能测试
- 结果播放和比较

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的错误处理部分
2. 检查示例应用的实现
3. 查看日志输出获取详细错误信息
4. 联系技术支持团队

## 版本历史

### v1.0.0 (当前版本)
- 初始发布
- 支持基本的音频编解码功能
- 提供Android SDK和示例应用
- 支持多种音频格式

## 许可证

本SDK采用商业许可证。使用前请仔细阅读许可证条款。
