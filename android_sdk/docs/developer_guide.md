# AudioCodec SDK 开发者指南

## 架构概述

AudioCodec SDK 采用分层架构设计，包含以下主要组件：

```
┌─────────────────────────────────────┐
│           Java/Kotlin API          │  ← 应用层接口
├─────────────────────────────────────┤
│            JNI 绑定层               │  ← Java与C++的桥梁
├─────────────────────────────────────┤
│           C++ 核心引擎              │  ← 核心算法实现
├─────────────────────────────────────┤
│         PyTorch Mobile             │  ← 深度学习推理
├─────────────────────────────────────┤
│        音频处理库 & 系统API         │  ← 底层音频处理
└─────────────────────────────────────┘
```

## 核心组件

### 1. AudioCodecSDK (Kotlin)
主要的SDK入口类，提供高级API接口。

**主要功能：**
- SDK生命周期管理
- 异步操作封装
- 错误处理和回调

**关键方法：**
```kotlin
suspend fun initialize(context: Context, config: CodecConfig): Boolean
fun encodeAudio(inputPath: String, callback: CodecCallback)
fun decodeAudio(codes: ByteArray, codesS: ByteArray, outputPath: String, callback: CodecCallback)
fun release()
```

### 2. AudioCodecJNI (Kotlin)
JNI接口类，负责与C++层通信。

**主要功能：**
- 调用native方法
- 数据类型转换
- 异常处理

### 3. CodecEngine (C++)
核心编解码引擎，实现具体的算法逻辑。

**主要功能：**
- 模型加载和管理
- 音频预处理
- 编解码算法实现
- 后处理和输出

## 编解码流程

### 编码流程

```mermaid
graph TD
    A[输入音频文件] --> B[音频加载]
    B --> C[重采样]
    C --> D[预处理]
    D --> E[Mel频谱提取]
    E --> F[PyTorch编码模型]
    F --> G[量化压缩]
    G --> H[输出编码数据]
```

**详细步骤：**

1. **音频加载**
   - 支持多种格式（WAV, MP3, M4A等）
   - 自动检测音频参数
   - 错误检查和验证

2. **重采样**
   - 统一采样率到目标值（通常16kHz）
   - 使用高质量重采样算法
   - 保持音频质量

3. **预处理**
   - 音频归一化
   - 预加重滤波
   - 静音检测和处理

4. **Mel频谱提取**
   - STFT变换
   - Mel滤波器组
   - 对数变换

5. **编码**
   - PyTorch模型推理
   - 向量量化
   - 压缩编码

### 解码流程

```mermaid
graph TD
    A[编码数据] --> B[反量化]
    B --> C[PyTorch解码模型]
    C --> D[Mel频谱重建]
    D --> E[音高生成]
    E --> F[音频合成]
    F --> G[后处理]
    G --> H[输出音频文件]
```

**详细步骤：**

1. **反量化**
   - 解析编码数据
   - 恢复量化参数
   - 重建特征向量

2. **Mel频谱重建**
   - PyTorch解码模型推理
   - 特征重建和平滑
   - 质量优化

3. **音高生成**
   - Mel2LF0模型推理
   - 基频估计
   - 音高轮廓生成

4. **音频合成**
   - NHV模型推理
   - 谐波和噪声合成
   - 高质量音频重建

5. **后处理**
   - 音频归一化
   - 格式转换
   - 文件保存

## 模型架构

### 1. 主编解码模型 (RefVQ)
- **文件**: `refvq_v1_310000_export.pt`
- **功能**: 音频特征的向量量化编解码
- **输入**: Mel频谱 (80 x T)
- **输出**: 量化编码 + 重建Mel频谱

### 2. 音高生成模型 (Mel2LF0)
- **文件**: `mel2lf0_model.ckpt-90000.pt`
- **功能**: 从Mel频谱生成基频信息
- **输入**: Mel频谱 (80 x T)
- **输出**: 对数基频序列 (T,)

### 3. 音频合成模型 (NHV)
- **文件**: `G3k_600000.pth`, `model.pth`
- **功能**: 从Mel频谱和音高合成高质量音频
- **输入**: Mel频谱 + 音高信息
- **输出**: 48kHz音频波形

## 性能优化

### 1. 内存优化

**策略：**
- 使用内存池管理大块内存
- 及时释放临时数据
- 避免不必要的数据拷贝

**实现：**
```cpp
class MemoryPool {
    std::vector<std::unique_ptr<float[]>> pool_;
    std::mutex mutex_;
    
public:
    float* acquire(size_t size);
    void release(float* ptr);
};
```

### 2. 计算优化

**策略：**
- 使用NEON指令集优化
- 并行处理多个音频帧
- 缓存频繁使用的计算结果

**实现：**
```cpp
// NEON优化的向量运算
void vectorMultiply_NEON(const float* a, const float* b, float* result, size_t size) {
    size_t neon_size = size & ~3;  // 4的倍数
    for (size_t i = 0; i < neon_size; i += 4) {
        float32x4_t va = vld1q_f32(&a[i]);
        float32x4_t vb = vld1q_f32(&b[i]);
        float32x4_t vr = vmulq_f32(va, vb);
        vst1q_f32(&result[i], vr);
    }
    // 处理剩余元素
    for (size_t i = neon_size; i < size; ++i) {
        result[i] = a[i] * b[i];
    }
}
```

### 3. 模型优化

**策略：**
- 模型量化（INT8）
- 算子融合
- 图优化

**实现：**
```cpp
// 模型量化配置
torch::jit::GraphOptimizerEnabledGuard guard(true);
auto optimized_model = torch::jit::optimize_for_inference(model);
```

## 错误处理机制

### 1. 分层错误处理

```cpp
enum class ErrorCode {
    SUCCESS = 0,
    INVALID_INPUT = 1001,
    FILE_NOT_FOUND = 1002,
    MEMORY_ALLOCATION_FAILED = 1003,
    MODEL_LOAD_FAILED = 1004,
    INFERENCE_FAILED = 1005,
    AUDIO_FORMAT_UNSUPPORTED = 1006
};

class AudioCodecException : public std::exception {
    ErrorCode code_;
    std::string message_;
    
public:
    AudioCodecException(ErrorCode code, const std::string& message)
        : code_(code), message_(message) {}
    
    ErrorCode getCode() const { return code_; }
    const char* what() const noexcept override { return message_.c_str(); }
};
```

### 2. 错误恢复策略

```cpp
class ErrorRecovery {
public:
    static bool tryRecover(ErrorCode code) {
        switch (code) {
            case ErrorCode::MEMORY_ALLOCATION_FAILED:
                return tryGarbageCollection();
            case ErrorCode::MODEL_LOAD_FAILED:
                return tryReloadModel();
            default:
                return false;
        }
    }
    
private:
    static bool tryGarbageCollection();
    static bool tryReloadModel();
};
```

## 测试框架

### 1. 单元测试

```cpp
#include <gtest/gtest.h>

class AudioProcessorTest : public ::testing::Test {
protected:
    void SetUp() override {
        processor_ = std::make_unique<AudioProcessor>();
    }
    
    std::unique_ptr<AudioProcessor> processor_;
};

TEST_F(AudioProcessorTest, ResampleTest) {
    std::vector<float> input = generateTestSignal(16000, 1.0f);
    std::vector<float> output = processor_->resample(input, 16000, 48000);
    
    EXPECT_EQ(output.size(), input.size() * 3);
    EXPECT_NEAR(calculateSNR(input, downsample(output)), 60.0f, 5.0f);
}
```

### 2. 集成测试

```kotlin
@Test
fun testFullCodecPipeline() = runTest {
    val sdk = AudioCodecSDK.getInstance()
    val config = CodecConfig.create16kConfig()
    
    assertTrue(sdk.initialize(context, config))
    
    val testAudioPath = copyTestAudioToCache()
    var encodeResult: CodecResult? = null
    
    // 测试编码
    sdk.encodeAudio(testAudioPath, object : CodecCallback {
        override fun onSuccess(result: CodecResult) {
            encodeResult = result
        }
        override fun onError(error: String) {
            fail("Encoding failed: $error")
        }
    })
    
    // 等待编码完成
    awaitUntil { encodeResult != null }
    
    assertNotNull(encodeResult)
    assertTrue(encodeResult!!.isSuccess)
    
    // 测试解码
    val outputPath = File(context.cacheDir, "decoded_test.wav").absolutePath
    var decodeResult: CodecResult? = null
    
    sdk.decodeAudio(
        encodeResult!!.codes!!,
        encodeResult!!.codesS!!,
        outputPath,
        object : CodecCallback {
            override fun onSuccess(result: CodecResult) {
                decodeResult = result
            }
            override fun onError(error: String) {
                fail("Decoding failed: $error")
            }
        }
    )
    
    // 等待解码完成
    awaitUntil { decodeResult != null }
    
    assertNotNull(decodeResult)
    assertTrue(decodeResult!!.isSuccess)
    assertTrue(File(outputPath).exists())
}
```

## 调试技巧

### 1. 日志系统

```cpp
#define LOG_LEVEL_VERBOSE 0
#define LOG_LEVEL_DEBUG   1
#define LOG_LEVEL_INFO    2
#define LOG_LEVEL_WARN    3
#define LOG_LEVEL_ERROR   4

class Logger {
public:
    static void setLevel(int level) { level_ = level; }
    
    template<typename... Args>
    static void log(int level, const char* tag, const char* format, Args... args) {
        if (level >= level_) {
            __android_log_print(level + ANDROID_LOG_VERBOSE, tag, format, args...);
        }
    }
    
private:
    static int level_;
};

#define LOGV(tag, ...) Logger::log(LOG_LEVEL_VERBOSE, tag, __VA_ARGS__)
#define LOGD(tag, ...) Logger::log(LOG_LEVEL_DEBUG, tag, __VA_ARGS__)
#define LOGI(tag, ...) Logger::log(LOG_LEVEL_INFO, tag, __VA_ARGS__)
#define LOGW(tag, ...) Logger::log(LOG_LEVEL_WARN, tag, __VA_ARGS__)
#define LOGE(tag, ...) Logger::log(LOG_LEVEL_ERROR, tag, __VA_ARGS__)
```

### 2. 性能分析

```cpp
class Profiler {
    std::unordered_map<std::string, std::chrono::high_resolution_clock::time_point> start_times_;
    std::unordered_map<std::string, long> total_times_;
    
public:
    void start(const std::string& name) {
        start_times_[name] = std::chrono::high_resolution_clock::now();
    }
    
    void end(const std::string& name) {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_times_[name]).count();
        total_times_[name] += duration;
    }
    
    void report() {
        for (const auto& [name, time] : total_times_) {
            LOGI("Profiler", "%s: %ld μs", name.c_str(), time);
        }
    }
};

// 使用宏简化性能分析
#define PROFILE_SCOPE(name) \
    ProfilerScope _prof_scope(name)

class ProfilerScope {
    std::string name_;
public:
    ProfilerScope(const std::string& name) : name_(name) {
        Profiler::getInstance().start(name_);
    }
    ~ProfilerScope() {
        Profiler::getInstance().end(name_);
    }
};
```

## 扩展开发

### 1. 添加新的音频格式支持

```cpp
class AudioFormatRegistry {
    std::unordered_map<std::string, std::unique_ptr<AudioFormatHandler>> handlers_;
    
public:
    void registerHandler(const std::string& extension, 
                        std::unique_ptr<AudioFormatHandler> handler) {
        handlers_[extension] = std::move(handler);
    }
    
    AudioFormatHandler* getHandler(const std::string& extension) {
        auto it = handlers_.find(extension);
        return it != handlers_.end() ? it->second.get() : nullptr;
    }
};

class MP3FormatHandler : public AudioFormatHandler {
public:
    bool canHandle(const std::string& file_path) override {
        return file_path.ends_with(".mp3");
    }
    
    std::vector<float> load(const std::string& file_path, int& sample_rate) override {
        // MP3解码实现
    }
    
    bool save(const std::string& file_path, const std::vector<float>& audio_data, 
              int sample_rate) override {
        // MP3编码实现
    }
};
```

### 2. 自定义编解码算法

```cpp
class CustomCodec : public CodecInterface {
public:
    CodecResult encode(const std::vector<std::vector<float>>& mel_spectrogram) override {
        // 自定义编码算法实现
    }
    
    std::vector<std::vector<float>> decode(const std::vector<uint8_t>& codes,
                                          const std::vector<uint8_t>& codes_s) override {
        // 自定义解码算法实现
    }
};

// 注册自定义编解码器
CodecRegistry::getInstance().registerCodec("custom", std::make_unique<CustomCodec>());
```

## 部署指南

### 1. 构建配置

```gradle
android {
    defaultConfig {
        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
    }
    
    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            ndk {
                debugSymbolLevel 'SYMBOL_TABLE'
            }
        }
    }
}
```

### 2. 模型文件打包

```gradle
android {
    sourceSets {
        main {
            assets.srcDirs = ['src/main/assets']
        }
    }
}

// 自动复制模型文件
task copyModels(type: Copy) {
    from '../core/models'
    into 'src/main/assets/models'
}

preBuild.dependsOn copyModels
```

### 3. 发布检查清单

- [ ] 所有模型文件已包含在assets中
- [ ] native库已正确编译和打包
- [ ] 权限配置正确
- [ ] 混淆规则已配置
- [ ] 性能测试通过
- [ ] 内存泄漏检查通过
- [ ] 多设备兼容性测试通过

## 最佳实践

### 1. 资源管理
- 始终在适当的时候调用 `release()`
- 使用 try-with-resources 模式
- 避免在主线程执行耗时操作

### 2. 错误处理
- 提供详细的错误信息
- 实现适当的重试机制
- 记录关键操作的日志

### 3. 性能优化
- 使用对象池减少内存分配
- 缓存计算结果
- 合理使用多线程

### 4. 用户体验
- 提供进度回调
- 支持操作取消
- 优雅处理异常情况
