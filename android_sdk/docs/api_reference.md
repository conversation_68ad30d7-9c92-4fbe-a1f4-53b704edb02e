# AudioCodec SDK API 参考文档

## 核心类

### AudioCodecSDK

主要的SDK入口类，提供音频编解码功能。

#### 方法

##### getInstance()
```kotlin
companion object fun getInstance(): AudioCodecSDK
```
获取SDK的单例实例。

**返回值：** AudioCodecSDK实例

---

##### initialize()
```kotlin
suspend fun initialize(context: Context, config: CodecConfig = CodecConfig()): <PERSON><PERSON>an
```
初始化SDK。

**参数：**
- `context: Context` - Android上下文
- `config: CodecConfig` - 编解码配置（可选）

**返回值：** Boolean - 是否初始化成功

**示例：**
```kotlin
val success = audioCodecSDK.initialize(this, CodecConfig.create16kConfig())
```

---

##### encodeAudio()
```kotlin
fun encodeAudio(inputPath: String, callback: Codec<PERSON>allback)
```
编码音频文件。

**参数：**
- `inputPath: String` - 输入音频文件路径
- `callback: CodecCallback` - 编码结果回调

**示例：**
```kotlin
audioCodecSDK.encodeAudio("/path/to/audio.wav", object : CodecCallback {
    override fun onSuccess(result: CodecResult) {
        // 处理成功结果
    }
    override fun onError(error: String) {
        // 处理错误
    }
})
```

---

##### decodeAudio()
```kotlin
fun decodeAudio(codes: ByteArray, codesS: ByteArray, outputPath: String, callback: CodecCallback)
```
解码音频数据到文件。

**参数：**
- `codes: ByteArray` - 主编码数据
- `codesS: ByteArray` - 辅助编码数据
- `outputPath: String` - 输出文件路径
- `callback: CodecCallback` - 解码结果回调

---

##### release()
```kotlin
fun release()
```
释放SDK资源。

**注意：** 应在应用退出或不再使用SDK时调用。

---

### CodecConfig

编解码配置类。

#### 构造函数
```kotlin
data class CodecConfig(
    val useCuda: Boolean = false,
    val sampleRate: Int = 16000,
    val hopSize: Int = 200,
    val winSize: Int = 800,
    val nFft: Int = 1024,
    val fMin: Int = 0,
    val fMax: Int = 8000,
    val nMels: Int = 80,
    val minDb: Float = -115f,
    val refDb: Float = 20f,
    val maxAbsValue: Float = 1.0f
)
```

#### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| useCuda | Boolean | false | 是否使用CUDA加速 |
| sampleRate | Int | 16000 | 音频采样率(Hz) |
| hopSize | Int | 200 | 跳跃大小 |
| winSize | Int | 800 | 窗口大小 |
| nFft | Int | 1024 | FFT大小 |
| fMin | Int | 0 | 最小频率(Hz) |
| fMax | Int | 8000 | 最大频率(Hz) |
| nMels | Int | 80 | Mel频谱通道数 |
| minDb | Float | -115f | 最小dB值 |
| refDb | Float | 20f | 参考dB值 |
| maxAbsValue | Float | 1.0f | 最大绝对值 |

#### 静态方法

##### create16kConfig()
```kotlin
companion object fun create16kConfig(): CodecConfig
```
创建16kHz采样率的预设配置。

**返回值：** 适用于语音处理的配置

---

##### create48kConfig()
```kotlin
companion object fun create48kConfig(): CodecConfig
```
创建48kHz采样率的预设配置。

**返回值：** 适用于音乐处理的配置

---

### CodecResult

编解码结果类。

#### 属性

```kotlin
data class CodecResult(
    val isSuccess: Boolean,
    val codes: ByteArray? = null,
    val codesS: ByteArray? = null,
    val compressedSize: Int = 0,
    val outputPath: String? = null,
    val processingTimeMs: Long = 0,
    val audioDurationSec: Float = 0f,
    val errorMessage: String? = null,
    val extraInfo: Map<String, Any> = emptyMap()
)
```

| 属性 | 类型 | 说明 |
|------|------|------|
| isSuccess | Boolean | 操作是否成功 |
| codes | ByteArray? | 主编码数据（编码时） |
| codesS | ByteArray? | 辅助编码数据（编码时） |
| compressedSize | Int | 压缩后大小（比特） |
| outputPath | String? | 输出文件路径（解码时） |
| processingTimeMs | Long | 处理耗时（毫秒） |
| audioDurationSec | Float | 音频时长（秒） |
| errorMessage | String? | 错误信息 |
| extraInfo | Map<String, Any> | 额外信息 |

#### 静态方法

##### encodeSuccess()
```kotlin
companion object fun encodeSuccess(
    codes: ByteArray,
    codesS: ByteArray,
    compressedSize: Int,
    processingTimeMs: Long,
    audioDurationSec: Float
): CodecResult
```
创建成功的编码结果。

---

##### decodeSuccess()
```kotlin
companion object fun decodeSuccess(
    outputPath: String,
    processingTimeMs: Long,
    audioDurationSec: Float
): CodecResult
```
创建成功的解码结果。

---

##### failure()
```kotlin
companion object fun failure(errorMessage: String): CodecResult
```
创建失败结果。

---

### CodecCallback

编解码回调接口。

#### 方法

##### onSuccess()
```kotlin
fun onSuccess(result: CodecResult)
```
操作成功时调用。

**参数：**
- `result: CodecResult` - 操作结果

---

##### onError()
```kotlin
fun onError(error: String)
```
操作失败时调用。

**参数：**
- `error: String` - 错误信息

---

##### onProgress()
```kotlin
fun onProgress(progress: Int)
```
进度更新时调用（可选实现）。

**参数：**
- `progress: Int` - 进度百分比(0-100)

---

### SimpleCodecCallback

简化的回调实现类。

#### 构造函数
```kotlin
class SimpleCodecCallback(
    private val onSuccessCallback: (CodecResult) -> Unit,
    private val onErrorCallback: (String) -> Unit,
    private val onProgressCallback: ((Int) -> Unit)? = null
) : CodecCallback
```

**示例：**
```kotlin
val callback = SimpleCodecCallback(
    onSuccessCallback = { result ->
        println("Success: ${result.compressedSize} bits")
    },
    onErrorCallback = { error ->
        println("Error: $error")
    },
    onProgressCallback = { progress ->
        println("Progress: $progress%")
    }
)
```

---

## JNI接口

### AudioCodecJNI

底层JNI接口类（通常不直接使用）。

#### 方法

##### initialize()
```kotlin
external fun initialize(
    modelPath: String,
    useCuda: Boolean,
    sampleRate: Int,
    hopSize: Int
): Boolean
```

##### encodeAudio()
```kotlin
external fun encodeAudio(inputPath: String): CodecResult
```

##### decodeAudio()
```kotlin
external fun decodeAudio(
    codes: ByteArray,
    codesS: ByteArray,
    outputPath: String
): CodecResult
```

##### encodeAudioData()
```kotlin
external fun encodeAudioData(
    audioData: FloatArray,
    sampleRate: Int
): CodecResult
```

##### decodeAudioData()
```kotlin
external fun decodeAudioData(
    codes: ByteArray,
    codesS: ByteArray
): CodecResult
```

##### getCodecInfo()
```kotlin
external fun getCodecInfo(): String
```

##### setLogLevel()
```kotlin
external fun setLogLevel(level: Int)
```

##### release()
```kotlin
external fun release()
```

##### isInitialized()
```kotlin
external fun isInitialized(): Boolean
```

##### getSupportedFormats()
```kotlin
external fun getSupportedFormats(): Array<String>
```

##### validateAudioFile()
```kotlin
external fun validateAudioFile(filePath: String): Boolean
```

##### getAudioInfo()
```kotlin
external fun getAudioInfo(filePath: String): String
```

---

## 常量和枚举

### 支持的音频格式

```kotlin
val SUPPORTED_INPUT_FORMATS = arrayOf("wav", "mp3", "m4a", "aac", "flac")
val SUPPORTED_OUTPUT_FORMATS = arrayOf("wav")
```

### 错误代码

```kotlin
object ErrorCodes {
    const val SUCCESS = 0
    const val INVALID_INPUT = 1001
    const val FILE_NOT_FOUND = 1002
    const val MEMORY_ALLOCATION_FAILED = 1003
    const val MODEL_LOAD_FAILED = 1004
    const val INFERENCE_FAILED = 1005
    const val AUDIO_FORMAT_UNSUPPORTED = 1006
}
```

### 日志级别

```kotlin
object LogLevel {
    const val VERBOSE = 0
    const val DEBUG = 1
    const val INFO = 2
    const val WARN = 3
    const val ERROR = 4
}
```

---

## 使用示例

### 完整的编解码流程

```kotlin
class AudioCodecExample {
    private val audioCodecSDK = AudioCodecSDK.getInstance()
    
    suspend fun performCodecTest(inputPath: String, outputPath: String) {
        // 1. 初始化SDK
        val config = CodecConfig.create16kConfig()
        if (!audioCodecSDK.initialize(this, config)) {
            throw RuntimeException("Failed to initialize SDK")
        }
        
        // 2. 编码音频
        val encodeResult = suspendCoroutine<CodecResult> { continuation ->
            audioCodecSDK.encodeAudio(inputPath, object : CodecCallback {
                override fun onSuccess(result: CodecResult) {
                    continuation.resume(result)
                }
                override fun onError(error: String) {
                    continuation.resumeWithException(RuntimeException(error))
                }
            })
        }
        
        if (!encodeResult.isSuccess) {
            throw RuntimeException("Encoding failed")
        }
        
        // 3. 解码音频
        val decodeResult = suspendCoroutine<CodecResult> { continuation ->
            audioCodecSDK.decodeAudio(
                encodeResult.codes!!,
                encodeResult.codesS!!,
                outputPath,
                object : CodecCallback {
                    override fun onSuccess(result: CodecResult) {
                        continuation.resume(result)
                    }
                    override fun onError(error: String) {
                        continuation.resumeWithException(RuntimeException(error))
                    }
                }
            )
        }
        
        if (!decodeResult.isSuccess) {
            throw RuntimeException("Decoding failed")
        }
        
        // 4. 输出结果
        println("Original duration: ${encodeResult.audioDurationSec}s")
        println("Compressed size: ${encodeResult.compressedSize} bits")
        println("Compression ratio: ${calculateCompressionRatio(encodeResult)}")
        println("Processing time: ${encodeResult.processingTimeMs + decodeResult.processingTimeMs}ms")
        
        // 5. 释放资源
        audioCodecSDK.release()
    }
    
    private fun calculateCompressionRatio(result: CodecResult): Float {
        val originalBits = result.audioDurationSec * 16000 * 16  // 16kHz, 16-bit
        return originalBits / result.compressedSize
    }
}
```

### 错误处理示例

```kotlin
class ErrorHandlingExample {
    fun handleCodecErrors(callback: CodecCallback): CodecCallback {
        return object : CodecCallback {
            override fun onSuccess(result: CodecResult) {
                callback.onSuccess(result)
            }
            
            override fun onError(error: String) {
                when {
                    error.contains("File not found") -> {
                        // 处理文件不存在错误
                        showFileNotFoundDialog()
                    }
                    error.contains("Memory allocation failed") -> {
                        // 处理内存不足错误
                        freeMemoryAndRetry()
                    }
                    error.contains("Model load failed") -> {
                        // 处理模型加载失败
                        reinstallModels()
                    }
                    else -> {
                        // 处理其他错误
                        callback.onError(error)
                    }
                }
            }
            
            override fun onProgress(progress: Int) {
                callback.onProgress(progress)
            }
        }
    }
}
```
