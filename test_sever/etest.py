import requests

def test_asr_service(audio_file_path, call_id):
    url = "http://127.0.0.1:38004/api/v1/receive_audio"
    files = {'audio_file': open(audio_file_path, 'rb')}
    data = {'id': call_id}
    
    response = requests.post(url, files=files, data=data)
    
    print("Response Status Code:", response.status_code)
    print("Response JSON:", response.json())

if __name__ == "__main__":
    test_asr_service("/home/<USER>/clips/cut_1.wav", "test_call_id")
