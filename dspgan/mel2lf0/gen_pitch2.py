import torch
import argparse
import os
import sys
import time
import numpy as np
from dspgan.mel2lf0.models.generator import Generator
from dspgan.mel2lf0.utils.utils import YParams
from dspgan.mel2lf0.utils.utils import gentable

from tqdm import tqdm
global_model = None

def initialize_model(yaml_conf, hparams, resume, use_cuda):
    global global_model
    if global_model is None:
        hparams_obj = YParams(yaml_conf)
        hp = hparams_obj.parse(hparams)
        global_model = create_model(hp)
        if resume is not None:
            attempt_to_restore(global_model, resume, use_cuda)
        device = torch.device("cuda" if use_cuda else "cpu")
        global_model.to(device)
        global_model.eval()
    return global_model

def attempt_to_restore(generate, checkpoint_dir, use_cuda):
    checkpoint_list = os.path.join(checkpoint_dir, 'checkpoint')

    if os.path.exists(checkpoint_list):
        checkpoint_filename = open(checkpoint_list).readline().strip()
        checkpoint_path = os.path.join(
            checkpoint_dir, "{}".format(checkpoint_filename))
        print("Restore from {}".format(checkpoint_path))
        checkpoint = load_checkpoint(checkpoint_path, use_cuda)
        generate.load_state_dict(checkpoint["generator"])

def load_checkpoint(checkpoint_path, use_cuda):
    if use_cuda:
        checkpoint = torch.load(checkpoint_path)
    else:
        checkpoint = torch.load(checkpoint_path,
                map_location=lambda storage, loc: storage)

    return checkpoint

def create_model(hp):

    generator = Generator(lc_channels=hp.acoustic_dim)

    return generator

def apply_gv(lf0, sex):
    if sex == "female":
       lf0 = (lf0 - 5.40929846) / 0.26657356 * 0.27670191 + 5.40831556
    else:
       lf0 = (lf0 - 4.82651584) / 0.32604051 * 0.34516617 + 4.82465317

    return lf0


def pitch_synthesis(mel_path, preemph=0.3, volume=2.0, sex="male", num_workers=4, 
                    resume="dspgan/mel2lf0/logdir/16k", use_cuda=False, yaml_conf='dspgan/mel2lf0/hparams.yaml', hparams= 'hop_size=200, win_size=800, sample_rate=16000'):
    global global_model
    
    if global_model is None:
        global_model = initialize_model(yaml_conf, hparams, resume, use_cuda)

    device = torch.device("cuda" if use_cuda else "cpu")

    conditions = mel_path
    if conditions.shape[0] != 80:
        conditions = conditions.transpose(1, 0)
    conditions = conditions * 4
    conditions = torch.FloatTensor(conditions).unsqueeze(0)
    conditions = conditions.to(device)

    start = time.time()
    try:
        lf0_data = global_model(conditions)
    except Exception as e:
        print(mel_path, e)
        sys.exit()
    time_used = time.time() - start

    lf0_data = lf0_data.detach().cpu().numpy().squeeze()
    lf0, uv = lf0_data[:, 0:1], lf0_data[:, 1:]
    lf0[uv < 0.5] = 0
    lf0 = lf0.squeeze()
    
    return lf0


