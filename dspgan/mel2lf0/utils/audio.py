import librosa
import librosa.filters
import math
import numpy as np
from scipy import signal
from scipy.io import wavfile
import soundfile as sf

def load_wav(path, sample_rate=16000):
  x = librosa.core.load(path, sr=sample_rate)[0]
  if len(x.shape) >1:
     x = x[0]
  return x

def save_wav(wav, path, hparams, norm=False):
  if norm:
      wav *= 32767 / max(0.01, np.max(np.abs(wav)))
      wavfile.write(path, hparams.sample_rate, wav.astype(np.int16))
  else:
      sf.write(path, wav, hparams.sample_rate)

def preemphasis(x, hp):
  return signal.lfilter([1, -hp.preemphasis], [1], x)

def inv_preemphasis(x, hp):
  return signal.lfilter([1], [1, -hp.preemphasis], x)

def spectrogram(y, hp):
  D = _stft(preemphasis(y, hp), hp)
  S = _amp_to_db(np.abs(D)) - hp.ref_level_db
  return _normalize(S, hp)

def inv_spectrogram(spectrogram, hp):
  '''Converts spectrogram to waveform using librosa'''
  S = _db_to_amp(_denormalize(spectrogram, hp) + hp.ref_level_db)  # Convert back to linear
  return inv_preemphasis(_griffin_lim(S ** hp.power), hp)          # Reconstruct phase

def melspectrogram(y, hp):
  D = _stft(preemphasis(y, hp), hp)
  S = _amp_to_db(_linear_to_mel(np.abs(D), hp)) - hp.ref_level_db
  return _normalize(S, hp)

def _griffin_lim(S, hp):
  '''librosa implementation of Griffin-Lim
  Based on https://github.com/librosa/librosa/issues/434
  '''
  angles = np.exp(2j * np.pi * np.random.rand(*S.shape))
  S_complex = np.abs(S).astype(np.complex)
  y = _istft(S_complex * angles)
  for i in range(hparams.griffin_lim_iters):
    angles = np.exp(1j * np.angle(_stft(y, hp)))
    y = _istft(S_complex * angles)
  return y

def _stft(y, hp):
  n_fft = (hp.num_freq - 1) * 2
  return librosa.stft(y=y, n_fft=n_fft, hop_length=hp.hop_size, win_length=hp.win_size)

def _istft(y, hp):
  return librosa.istft(y, hop_length=hp.hop_size, win_length=hp.win_size)

_mel_basis = None
_inv_mel_basis = None

def _linear_to_mel(spectrogram, hp):
  global _mel_basis
  if _mel_basis is None:
    _mel_basis = _build_mel_basis(hp)
  return np.dot(_mel_basis, spectrogram)

def inv_mel_spectrogram(mel_spectrogram, hp):
  S = _mel_to_linear(_db_to_amp(_denormalize(mel_spectrogram, hp) + hp.ref_level_db)) # Convert back to linear
  return inv_preemphasis(_griffin_lim(S ** hp.power), hp)          # Reconstruct phase

def _mel_to_linear(mel_spectrogram):
  global _inv_mel_basis
  if _inv_mel_basis is None:
    _inv_mel_basis = np.linalg.pinv(_build_mel_basis())
  return np.maximum(1e-10, np.dot(_inv_mel_basis, mel_spectrogram))

def _build_mel_basis(hp):
  n_fft = (hp.num_freq - 1) * 2
  return librosa.filters.mel(hp.sample_rate, n_fft, n_mels=hp.acoustic_dim)

def _amp_to_db(x):
  return 20 * np.log10(np.maximum(1e-5, x))

def _db_to_amp(x):
  return np.power(10.0, x * 0.05)

def _normalize(S, hp):
  return np.clip((S - hp.min_level_db) / -hp.min_level_db, 0, 1)

def _denormalize(S, hp):
  return (np.clip(S, 0, 1) * -hp.min_level_db) + hp.min_level_db
