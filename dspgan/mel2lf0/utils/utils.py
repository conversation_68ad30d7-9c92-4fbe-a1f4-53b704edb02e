"""utils"""

from ruamel.yaml import YAML as yaml
from dspgan.mel2lf0.utils.hparam import HParams
import collections
import os
import argparse
import numpy as np
import pickle
#import zstd

def gentable(phaseslot=4000, sample_rate=16000, minf0=20, maxf0=1000):
    maxhar = int(sample_rate/2/minf0)
    minhar = int(sample_rate /2/ maxf0)
    phase=np.reshape(np.asarray([i * 2*np.pi / phaseslot for i in range(phaseslot)]), (1, -1))
    sinarray=np.sin(np.concatenate([i * phase for i in range(1, maxhar+1)], axis=0))
    sin_table = np.zeros((maxhar-minhar+1, phaseslot),dtype=np.float32)
    for i in range(minhar,maxhar+1):
        sin_table[i-minhar] = np.sum(sinarray[:i],axis=0)/np.sqrt(i)
    data=dict()
    data['minf0']=minf0
    data['phaseslot']=phaseslot
    data['maxf0']=maxf0
    data['maxhar']=maxhar
    data['minhar']=minhar
    data['sample_rate']=sample_rate
    data['sin_table']=np.reshape(sin_table, (-1))
    #data=pickle.dumps(data)
    #cdata=zstd.compress(data, 9)
    #f_d=open(outfile, 'wb')
    #f_d.write(cdata)
    #f_d.close()
    return data


def lf0tosinexi(exi_data, lf0, upsample_factor, sample_rate):
    #f0_interval = 0.1
    phase_interval = np.pi * 2 / exi_data['phaseslot']
    f0 = lf0.astype(np.float32)
    f0[f0>0] = np.clip(np.exp(f0[f0>0]), exi_data['minf0'], exi_data['maxf0'])
    #f0=np.clip(f0, exi_data['minf0'], exi_data['maxf0'])
    phase_tmp = np.zeros((upsample_factor+1), dtype=np.float32)
    exi = np.zeros((lf0.shape[0]*upsample_factor), dtype=np.float32)
    uv = np.zeros((lf0.shape[0]*upsample_factor), dtype=np.float32)
    f0idx=[]
    phaseidx=[]
    for i in range(f0.shape[0]):
        if f0[i] > 0:
            uv[i*upsample_factor:(i+1)*upsample_factor]=1
            if i < f0.shape[0]-1 and f0[i+1]>0:
                f0_inc = (f0[i+1]-f0[i]) / upsample_factor
            else:
                f0_inc = 0
            f0_tmp2 = np.asarray([f0[i] + j * f0_inc for j in range(upsample_factor)])
            f0_tmp = f0_tmp2 * 2 * np.pi / sample_rate
            phase_tmp[1:] = f0_tmp
            phase_tmp = np.cumsum(phase_tmp, axis=0)
            phase_tmp = np.fmod(phase_tmp, 2*np.pi)
            phase_tmp2=phase_tmp/phase_interval
            f0idx.append(f0_tmp2)
            phaseidx.append(phase_tmp2[1:])
            phase_tmp[0] = phase_tmp[-1]
        else:
            phase_tmp[0] = 0
    haridx = (sample_rate / 2 / np.concatenate(f0idx)).astype(np.int32) - exi_data['minhar']
    #f0idx=np.round((np.concatenate(f0idx)-20)/f0_interval).astype(np.int32)
    phaseidx=np.round(np.concatenate(phaseidx)).astype(np.int32)
    phaseidx[phaseidx==exi_data['phaseslot']]=0
    #print(haridx.shape, phaseidx[:,0].shape)
    #exit()
    exi[uv>0]=exi_data['sin_table'][haridx * exi_data['phaseslot'] + phaseidx]
    #exi[uv>0] = exi_table_2[f0idx[:,0] * exi_table.shape[1] + phaseidx[:,0]]
    return exi

def lf0toexi(lf0, upsample_factor, sample_rate):
    exi=np.zeros((lf0.shape[0]*upsample_factor,1), dtype=np.float32)
    phase=0
    f0=lf0
    f0[f0>0]=sample_rate / np.exp(f0[f0>0])
    for i in range(f0.shape[0]):
        if i == f0.shape[0]-1:
            inc=0
        else:
            if f0[i+1,0]>0:
                inc = (f0[i+1,0]-f0[i,0]) / upsample_factor
            else:
                inc = 0
        if f0[i,0]>0:
            f0_tmp = f0[i,0]
            for j in range(upsample_factor):
                phase = phase + 1;
                if phase > f0_tmp:
                    phase = phase - f0_tmp
                    exi[i * upsample_factor + j] = np.sqrt(f0_tmp)
                f0_tmp = f0_tmp + inc
        else:
            #for j in range(upsample_factor):
            #    exi[i * upsample_factor + j] = random.gauss(0, 1)
            phase = 0
    return exi

class ValueWindow():
    def __init__(self, window_size=100):
        self._window_size = window_size
        self._values = []

    def append(self, x):
        self._values = self._values[-(self._window_size - 1):] + [x]

    @property
    def sum(self):
        return sum(self._values)

    @property
    def count(self):
        return len(self._values)

    @property
    def average(self):
        return self.sum / max(1, self.count)

    def reset(self):
        self._values = []

class YParams(HParams):

    def __init__(self, yaml_file):
        if not os.path.exists(yaml_file):
            raise IOError("yaml file: {} is not existed".format(yaml_file))
        super().__init__()
        self.d = collections.OrderedDict()
        with open(yaml_file) as fp:
            for _, v in yaml().load(fp).items():
                for k1, v1 in v.items():
                    try:
                        if self.get(k1):
                            self.set_hparam(k1, v1)
                        else:
                            self.add_hparam(k1, v1)
                        self.d[k1] = v1
                    except Exception:
                        import traceback
                        print(traceback.format_exc())

    # @property
    def get_elements(self):
        return self.d.items()


def str2bool(v):
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise ValueError('Unsupported value encountered.')
 

def read_binary_file(filename, dimension=None):
    """Read data from matlab binary file (row, col and matrix).

    Returns:
        A numpy matrix containing data of the given binary file.
    """
    fid_lab = open(filename, 'rb')
    features = np.fromfile(fid_lab, dtype=np.float32)
    fid_lab.close()
    assert features.size % float(dimension) == 0.0, 'specified dimension %s not compatible with data' % (
        dimension)
    features = features[:(dimension * (features.size // dimension))]
    features = features.reshape((-1, dimension))
    return features, features.shape[0] 

def write_binary_file(data, output_file_name):
        data = np.asarray(data, np.float32)
        fid = open(output_file_name, 'wb')
        data.tofile(fid)
        fid.close()
