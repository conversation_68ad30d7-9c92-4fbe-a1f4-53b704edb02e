import numpy as np
import os
import torch
from torch.utils.data import Dataset
import random
import librosa
from scipy.io import wavfile
from scipy.interpolate import interp1d
import librosa

class CustomerDataset(Dataset):
    def __init__(self,
                 path,
                 sample_size=16000,
                 upsample_factor=256,
                 local_condition=True,
                 global_condition=False):

        self.path = path
        self.metadata = self.get_metadata(path)
        random.shuffle(self.metadata)

        self.condition_window = 150
        self.sample_window = sample_size
        self.upsample_factor = upsample_factor
        self.local_condition = local_condition
        self.global_condition = global_condition

    def __getitem__(self, index):
        #condition = np.load(os.path.join(self.path, 'mel', self.metadata[index]+'.npy')).transpose(1, 0)
        condition = np.load(os.path.join(self.path, 'mel', self.metadata[index]+'.npy')).transpose(1, 0)

        # condition = (condition * 2.) - 1.
        #condition = np.fromfile(os.path.join(self.path, 'mel', self.metadata[index] + '.dat'), 'float32').reshape(-1, 80)

        #lf0 = np.fromfile(os.path.join(self.path, 'lf0', self.metadata[index] + '.lf0'), 'float32').reshape(-1, 1)
        lf0 = np.load(os.path.join(self.path, 'lf0', self.metadata[index] + '.npy')).reshape(-1, 1)
        length = min([len(condition), len(lf0)])
        condition = condition[:length , :]
        lf0 = lf0[:length, :]
        uv = np.zeros(lf0.shape, dtype=np.float32)
        lf0 = np.exp(lf0)
        lf0 = np.where(lf0 > 50., np.log(lf0), 0.)
        uv[lf0 > 0] = 1

        if len(condition) < self.condition_window:
            condition = np.pad(condition, [[0, self.condition_window - len(condition)], [0, 0]], 'edge')
            lf0 = np.pad(lf0, [[0, self.condition_window - len(lf0)], [0, 0]], 'edge')
            uv = np.pad(uv, [[0, self.condition_window - len(uv)], [0, 0]], 'edge')
        elif len(condition) > self.condition_window:
            lc_index = np.random.randint(0, len(condition) - self.condition_window)
            condition = condition[lc_index : (lc_index + self.condition_window)]
            lf0 = lf0[lc_index : (lc_index + self.condition_window)]
            uv = uv[lc_index : (lc_index + self.condition_window)]

        return condition, self.func_interp1d(lf0), uv

    def func_interp1d(self, pitch):
        pitch = pitch.reshape(-1)
        nonzero_ids = np.where(pitch != 0)[0]
        if len(nonzero_ids) > 1:
           interp_fn = interp1d(
               nonzero_ids,
               pitch[nonzero_ids],
               fill_value=(pitch[nonzero_ids[0]], pitch[nonzero_ids[-1]]),
               bounds_error=False,
           )
           pitch = interp_fn(np.arange(0, len(pitch)))

        return pitch.reshape(-1, 1)

    def __len__(self):
        return len(self.metadata)

    def get_metadata(self, path):
        metadata = []
        for filename in os.listdir(os.path.join(path, 'lf0')):
           filename = filename.replace('.npy', '')
           metadata.append(filename)

        return metadata

class CustomerCollate(object):

    def __init__(self):
        pass

    def __call__(self, batch):
        return self._collate_fn(batch)

    def _collate_fn(self, batch):

        condition_batch = []
        lf0_batch = []
        uv_batch = []
        for (i, x) in enumerate(batch):
            condition_batch.append(x[0])
            lf0_batch.append(x[1])
            uv_batch.append(x[2])
        condition_batch = np.stack(condition_batch)
        lf0_batch = np.stack(lf0_batch)
        uv_batch = np.stack(uv_batch)

        conditions = torch.FloatTensor(condition_batch).transpose(1, 2)
        lf0 = torch.FloatTensor(lf0_batch)
        uv = torch.FloatTensor(uv_batch)

        return conditions, lf0, uv
