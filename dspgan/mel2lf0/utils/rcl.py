import torch
import torch.nn as nn
import numpy as np

class SRCLLoss(nn.Module):
    def __init__(self):
        super(SRCLLoss, self).__init__()

    def forward(self, predicts, targets):
        """
        Args:
            x: predicted signal (B, T).
            y: truth signal (B, T).

        Returns:
            Tensor: STFT loss values.
        """
        p_mean = torch.mean(predicts, dim=1, keepdim=True)
        t_mean = torch.mean(targets, dim=1, keepdim=True)
        x = predicts - p_mean
        y = targets - t_mean
        a = torch.sum(x * y, dim=1, keepdim=True)
        b = torch.sqrt(torch.sum(x**2, dim=1, keepdim=True) * torch.sum(y**2, dim=1, keepdim=True))
        loss = torch.mean(a / b)

        return loss

class RCLLoss(nn.Module):
    def __init__(self,
                 L=1600,
                 shift=800):
        super(RCLLoss, self).__init__()

        self.L = L
        self.shift = shift
        self.loss = SRCLLoss()

    def forward(self, predicts, targets):
        """
        Args:
            x: predicted signal (B, T).
            y: truth signal (B, T).

        Returns:
            Tensor: STFT loss values.
        """
        length = predicts.size(1)
        lists = []
        for i in range(0, length - self.L, self.shift):
            index = np.random.randint(0, length - self.L)
            a = self.loss(predicts[:, i : i + self.L], predicts[:, index : index + self.L])
            b = self.loss(targets[:, i : i + self.L], targets[:, index : index + self.L])
            lists.append((a - b)**2) 
        
        loss = sum(lists)
 
        return loss

if __name__ == "__main__":
   model = RCLLoss()
   x = torch.tanh(torch.randn(2, 16000))
   y = torch.tanh(torch.randn(2, 16000))
   print(x.numpy().max(), x.numpy().min())
   loss = model(x, y)
   print(loss)
