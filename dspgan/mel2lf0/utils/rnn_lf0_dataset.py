import numpy as np
import os
import torch
from torch.utils.data import Dataset
import random
import librosa
from scipy.io import wavfile
from scipy.interpolate import interp1d
from utils.utils import lf0tosinexi, loadexidata, gentable
import librosa

class CustomerDataset(Dataset):
    def __init__(self,
                 path,
                 sample_size=16000,
                 upsample_factor=256,
                 local_condition=True,
                 global_condition=False):

        self.path = path
        self.metadata = self.get_metadata(path)
        random.shuffle(self.metadata)

        self.condition_window = sample_size // upsample_factor
        self.sample_window = sample_size
        self.upsample_factor = upsample_factor
        self.local_condition = local_condition
        self.global_condition = global_condition

    def __getitem__(self, index):

        sample = np.fromfile(os.path.join(self.path, 'audio', self.metadata[index] + '.raw'), 'float32').reshape(-1, 1)
        condition = np.fromfile(os.path.join(self.path, 'mel', self.metadata[index] + '.dat'), 'float32').reshape(-1, 80)
        lf0 = np.fromfile(os.path.join(self.path, 'rnn_lf0', self.metadata[index] + '.lf0'), 'float32').reshape(-1, 1)[::2]
        exi = np.fromfile(os.path.join(self.path, 'rnn_exi', self.metadata[index] + '.raw'), 'float32').reshape(-1, 1)

        length = min([len(sample), len(condition) * self.upsample_factor, len(lf0) * self.upsample_factor])
        length = length // self.upsample_factor * self.upsample_factor

        condition = condition[:length // self.upsample_factor, :]
        lf0 = lf0[:length // self.upsample_factor, :]
        sample = sample[:length, :]
        exi = exi[:length, :]
        uv = np.zeros(lf0.shape, dtype=np.float32)
        uv[lf0 > 0] = 1

        if len(condition) < self.condition_window:
            sample = np.pad(sample, [[0, self.sample_window - len(sample)], [0, 0]], 'constant')
            condition = np.pad(condition, [[0, self.condition_window - len(condition)], [0, 0]], 'edge')
            exi = np.pad(exi, [[0, self.sample_window - len(exi)], [0, 0]], 'edge')
            uv = np.pad(uv, [[0, self.condition_window - len(uv)], [0, 0]], 'edge')
        elif len(condition) > self.condition_window:
            lc_index = np.random.randint(0, len(condition) - self.condition_window)
            sample = sample[lc_index * self.upsample_factor :
                (lc_index + self.condition_window) * self.upsample_factor]
            condition = condition[lc_index : (lc_index + self.condition_window)]
            exi = exi[lc_index * self.upsample_factor:
                (lc_index + self.condition_window) * self.upsample_factor]
            uv = uv[lc_index : (lc_index + self.condition_window)]
 
        return sample, condition, exi, uv

    def func_interp1d(self, pitch):
        pitch = pitch.reshape(-1)
        nonzero_ids = np.where(pitch != -1e10)[0]
        interp_fn = interp1d(
            nonzero_ids,
            pitch[nonzero_ids],
            fill_value=(pitch[nonzero_ids[0]], pitch[nonzero_ids[-1]]),
            bounds_error=False,
        )
        pitch = interp_fn(np.arange(0, len(pitch)))

        return pitch.reshape(-1, 1)

    def __len__(self):
        return len(self.metadata)

    def get_metadata(self, path):
        metadata = []
        for filename in os.listdir(os.path.join(path, 'rnn_exi')):
           filename = filename.split('.')[0]
           metadata.append(filename)

        return metadata

class CustomerCollate(object):

    def __init__(self):
        pass

    def __call__(self, batch):
        return self._collate_fn(batch)

    def _collate_fn(self, batch):

        sample_batch = []
        condition_batch = []
        exi_batch = []
        uv_batch = []
        for (i, x) in enumerate(batch):
            sample_batch.append(x[0])
            condition_batch.append(x[1])
            exi_batch.append(x[2])
            uv_batch.append(x[3])
        sample_batch = np.stack(sample_batch)
        condition_batch = np.stack(condition_batch)
        exi_batch = np.stack(exi_batch)
        uv_batch = np.stack(uv_batch)

        samples = torch.FloatTensor(sample_batch).transpose(1, 2)
        conditions = torch.FloatTensor(condition_batch).transpose(1, 2)
        exi = torch.FloatTensor(exi_batch).transpose(1, 2)
        uv = torch.FloatTensor(uv_batch)

        return samples, conditions, exi, uv
