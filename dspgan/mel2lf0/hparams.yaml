---
Data_settings:
    sample_rate: 16000
    preemphasis: 0.97
    min_level_db: -100
    ref_level_db: 20

Mel_settings:
    acoustic_dim: 80  # Dimension of acousitc spectrograms
    num_freq: 1025  # Only used when adding linear spectrograms post processing network
    hop_size: 160 # hop_size between acoustic feature and waveforms
    win_size: 400 # For 22050Hz 1100 ~:  50 ms (If None win_size :  n_fft)

Griffin Lim:
    power: 1.5 # power values for singals
    griffin_lim_iters: 100 # epochs for iteratively estimate phase

Model_settings:
    channels: [24, 48, 96, 192, 384]
    groups_channel: 24
    upsample_factor: [2, 4, 4, 5] # prod(upsample_factor) * num_bands == hop_size
    res_base: 2
    res_nums: 6
    num_dis: 3 # suggest 3 for 16kHz, 4 for 24kHz 6 for 32kHz
    periods: [2, 3, 5, 7, 11] # different period for multi_period_discriminator
    fre_hop_sizes: [40, 80, 160, 200, 240, 300] # different hop_size for frequence_discriminator
    fre_hidden_channels: [128, 128, 256, 256, 512, 512]
    use_multi_period: False
    use_multi_fre: True

    kl_weight_upper: 0.02
    kl_weight_step: 10000
    kl_weight_increment: 0.005

    # full band fft parms
    full_fft_sizes: [512, 1024, 1024]
    full_win_sizes: [320, 640, 800] # 20ms, 40ms, 50ms
    full_hop_sizes: [80, 160, 200] # 5ms,  10ms, 12.5ms
