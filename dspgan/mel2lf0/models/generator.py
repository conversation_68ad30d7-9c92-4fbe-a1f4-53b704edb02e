import torch
import torch.nn as nn
from torch.nn.utils import weight_norm
import numpy as np

def MLP(in_size, hidden_size, n_layers):
    channels = [in_size] + (n_layers) * [hidden_size]
    net = []
    for i in range(n_layers):
        net.append(nn.Linear(channels[i], channels[i + 1]))
        net.append(nn.LayerNorm(channels[i + 1]))
        net.append(nn.LeakyReLU())
    return nn.Sequential(*net)

class Harmonic(nn.Module):
   def __init__(self, indim, kdim):
       super(Harmonic, self).__init__()

       self.layer = nn.Sequential(
            nn.ReflectionPad1d(1),
            weight_norm(nn.Conv1d(indim, kdim, kernel_size=3)),
            nn.LeakyReLU(0.2, True),
            nn.ReflectionPad1d(1),
            weight_norm(nn.Conv1d(kdim, kdim, kernel_size=3)),
            nn.LeakyReLU(0.2, True),
            nn.ReflectionPad1d(1),
            weight_norm(nn.Conv1d(kdim, kdim, kernel_size=3)),
            nn.LeakyReLU(0.2, True),
            nn.ReflectionPad1d(1),
            weight_norm(nn.Conv1d(kdim, kdim, kernel_size=3)),
            nn.LeakyReLU(0.2, True),
            nn.ReflectionPad1d(1),
            weight_norm(nn.Conv1d(kdim, kdim, kernel_size=3)),
       )

       self.out_mlp = MLP(kdim + indim, kdim, 3)
       self.lf0_linear = nn.Linear(kdim, 2)

   def forward(self, indata):
       x = self.layer(indata.transpose(1, 2)).transpose(1, 2)
       x = self.out_mlp(torch.cat([x, indata], dim=-1))
 
       return self.lf0_linear(x)
 
class Generator(nn.Module):
    def __init__(self,
                 in_channels=1,
                 lc_channels=80):
        super(Generator, self).__init__()

        self.harmonic = Harmonic(lc_channels, 256)

        self.num_params()

    def forward(self, conditions):
        lf0_data = self.harmonic(conditions.transpose(1, 2))

        return lf0_data

    def num_params(self):
        parameters = [i[1] for i in self.named_parameters()]
        parameters = sum([np.prod(p.size()) for p in parameters])
        print('Trainable Parameters: %.3f million' % (parameters / 1_000_000))

if __name__ == '__main__':
    x = torch.randn(3, 100, 1)
    lc = torch.randn(3, 80, 100)
    model = Generator()
    signal = model(x, x, lc)
    print(signal.shape)
