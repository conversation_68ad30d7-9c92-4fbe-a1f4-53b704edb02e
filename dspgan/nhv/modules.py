import copy
import math
import numpy as np
import scipy
import torch
from torch import nn
from torch.nn import functional as F

from torch.nn import Conv1d, ConvTranspose1d, AvgPool1d, Conv2d
from torch.nn.utils import weight_norm, remove_weight_norm

import dspgan.nhv.commons
import dspgan.nhv.attentions
from dspgan.nhv.commons import init_weights, get_padding
from dspgan.nhv.transforms import piecewise_rational_quadratic_transform


LRELU_SLOPE = 0.1


class LayerNorm(nn.Module):
  def __init__(self, channels, eps=1e-5):
    super().__init__()
    self.channels = channels
    self.eps = eps

    self.gamma = nn.Parameter(torch.ones(channels))
    self.beta = nn.Parameter(torch.zeros(channels))

  def forward(self, x):
    x = x.transpose(1, -1)
    x = F.layer_norm(x, (self.channels,), self.gamma, self.beta, self.eps)
    return x.transpose(1, -1)

 
class ConvReluNorm(nn.Module):
  def __init__(self, in_channels, hidden_channels, out_channels, kernel_size, n_layers, p_dropout):
    super().__init__()
    self.in_channels = in_channels
    self.hidden_channels = hidden_channels
    self.out_channels = out_channels
    self.kernel_size = kernel_size
    self.n_layers = n_layers
    self.p_dropout = p_dropout
    assert n_layers > 1, "Number of layers should be larger than 0."

    self.conv_layers = nn.ModuleList()
    self.norm_layers = nn.ModuleList()
    self.conv_layers.append(nn.Conv1d(in_channels, hidden_channels, kernel_size, padding=kernel_size//2))
    self.norm_layers.append(LayerNorm(hidden_channels))
    self.relu_drop = nn.Sequential(
        nn.ReLU(),
        nn.Dropout(p_dropout))
    for _ in range(n_layers-1):
      self.conv_layers.append(nn.Conv1d(hidden_channels, hidden_channels, kernel_size, padding=kernel_size//2))
      self.norm_layers.append(LayerNorm(hidden_channels))
    self.proj = nn.Conv1d(hidden_channels, out_channels, 1)
    self.proj.weight.data.zero_()
    self.proj.bias.data.zero_()

  def forward(self, x, x_mask):
    x_org = x
    for i in range(self.n_layers):
      x = self.conv_layers[i](x * x_mask)
      x = self.norm_layers[i](x)
      x = self.relu_drop(x)
    x = x_org + self.proj(x)
    return x * x_mask

class DDSConv1d(nn.Module):
  """
  Dialted and Depth-Separable Convolution
  """
  def __init__(self, channels, kernel_size, dilation, p_dropout=0.):
    super().__init__()
    self.channels = channels
    self.kernel_size = kernel_size

    self.convs_sep = nn.ModuleList()
    self.convs_1x1 = nn.ModuleList()
    padding = (kernel_size * dilation - dilation) // 2
    self.convs_sep = weight_norm(nn.Conv1d(
        channels, channels, kernel_size, 
        groups=channels, dilation=dilation, padding=padding
    ))
    self.convs_sep.apply(init_weights)
    self.convs_1x1 = weight_norm(nn.Conv1d(channels, channels, 1))
    self.convs_1x1.apply(init_weights)

  def forward(self, x):
    y = self.convs_sep(x)
    y = F.gelu(y)
    y = self.convs_1x1(y)
    y = F.gelu(y)
    return x + y



class DDSConv(nn.Module):
  """
  Dialted and Depth-Separable Convolution
  """
  def __init__(self, channels, kernel_size, n_layers, p_dropout=0.):
    super().__init__()
    self.channels = channels
    self.kernel_size = kernel_size
    self.n_layers = n_layers
    self.p_dropout = p_dropout

    self.drop = nn.Dropout(p_dropout)
    self.convs_sep = nn.ModuleList()
    self.convs_1x1 = nn.ModuleList()
    self.norms_1 = nn.ModuleList()
    self.norms_2 = nn.ModuleList()
    for i in range(n_layers):
      dilation = kernel_size ** i
      padding = (kernel_size * dilation - dilation) // 2
      self.convs_sep.append(nn.Conv1d(channels, channels, kernel_size, 
          groups=channels, dilation=dilation, padding=padding
      ))
      self.convs_1x1.append(nn.Conv1d(channels, channels, 1))
      self.norms_1.append(LayerNorm(channels))
      self.norms_2.append(LayerNorm(channels))

  def forward(self, x, x_mask, g=None):
    if g is not None:
      x = x + g
    for i in range(self.n_layers):
      y = self.convs_sep[i](x * x_mask)
      y = self.norms_1[i](y)
      y = F.gelu(y)
      y = self.convs_1x1[i](y)
      y = self.norms_2[i](y)
      y = F.gelu(y)
      y = self.drop(y)
      x = x + y
    return x * x_mask


class WN(torch.nn.Module):
  def __init__(self, hidden_channels, kernel_size, dilation_rate, n_layers, gin_channels=0, p_dropout=0):
    super(WN, self).__init__()
    assert(kernel_size % 2 == 1)
    self.hidden_channels =hidden_channels
    self.kernel_size = kernel_size,
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.gin_channels = gin_channels
    self.p_dropout = p_dropout

    self.in_layers = torch.nn.ModuleList()
    self.res_skip_layers = torch.nn.ModuleList()
    self.drop = nn.Dropout(p_dropout)

    if gin_channels != 0:
      cond_layer = torch.nn.Conv1d(gin_channels, 2*hidden_channels*n_layers, 1)
      self.cond_layer = torch.nn.utils.weight_norm(cond_layer, name='weight')

    for i in range(n_layers):
      dilation = dilation_rate ** i
      padding = int((kernel_size * dilation - dilation) / 2)
      in_layer = torch.nn.Conv1d(hidden_channels, 2*hidden_channels, kernel_size,
                                 dilation=dilation, padding=padding)
      in_layer = torch.nn.utils.weight_norm(in_layer, name='weight')
      self.in_layers.append(in_layer)

      # last one is not necessary
      if i < n_layers - 1:
        res_skip_channels = 2 * hidden_channels
      else:
        res_skip_channels = hidden_channels

      res_skip_layer = torch.nn.Conv1d(hidden_channels, res_skip_channels, 1)
      res_skip_layer = torch.nn.utils.weight_norm(res_skip_layer, name='weight')
      self.res_skip_layers.append(res_skip_layer)

  def forward(self, x, x_mask, g=None, **kwargs):
    output = torch.zeros_like(x)
    n_channels_tensor = torch.IntTensor([self.hidden_channels])

    if g is not None:
      g = self.cond_layer(g)

    for i in range(self.n_layers):
      x_in = self.in_layers[i](x)
      if g is not None:
        cond_offset = i * 2 * self.hidden_channels
        g_l = g[:,cond_offset:cond_offset+2*self.hidden_channels,:]
      else:
        g_l = torch.zeros_like(x_in)

      acts = commons.fused_add_tanh_sigmoid_multiply(
          x_in,
          g_l,
          n_channels_tensor)
      acts = self.drop(acts)

      res_skip_acts = self.res_skip_layers[i](acts)
      if i < self.n_layers - 1:
        res_acts = res_skip_acts[:,:self.hidden_channels,:]
        x = (x + res_acts) * x_mask
        output = output + res_skip_acts[:,self.hidden_channels:,:]
      else:
        output = output + res_skip_acts
    return output * x_mask



class ResBlock1(torch.nn.Module):
    def __init__(self, channels, kernel_size=3, dilation=(1, 3, 5)):
        super(ResBlock1, self).__init__()
        self.convs1 = nn.ModuleList([
            Conv1d(channels, channels, kernel_size, 1, dilation=dilation[0],
                               padding=get_padding(kernel_size, dilation[0])),
            Conv1d(channels, channels, kernel_size, 1, dilation=dilation[1],
                               padding=get_padding(kernel_size, dilation[1])),
            Conv1d(channels, channels, kernel_size, 1, dilation=dilation[2],
                               padding=get_padding(kernel_size, dilation[2]))
        ])


        self.convs2 = nn.ModuleList([
            Conv1d(channels, channels, kernel_size, 1, dilation=1,
                               padding=get_padding(kernel_size, 1)),
            Conv1d(channels, channels, kernel_size, 1, dilation=1,
                               padding=get_padding(kernel_size, 1)),
            Conv1d(channels, channels, kernel_size, 1, dilation=1,
                               padding=get_padding(kernel_size, 1))
        ])

    def forward(self, x, x_mask=None):
        for c1, c2 in zip(self.convs1, self.convs2):
            xt = F.leaky_relu(x, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c1(xt)
            xt = F.leaky_relu(xt, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c2(xt)
            x = xt + x
        if x_mask is not None:
            x = x * x_mask
        return x


class ResBlock1_DDS(torch.nn.Module):
    def __init__(self, channels, kernel_size=3, dilation=(1, 3, 5)):
        super(ResBlock1_DDS, self).__init__()
        self.convs1 = nn.ModuleList([
            DDSConv1d(channels, kernel_size, dilation=dilation[0]),
            DDSConv1d(channels, kernel_size, dilation=dilation[1]),
            DDSConv1d(channels, kernel_size, dilation=dilation[2]),
        ])

        self.convs2 = nn.ModuleList([
            DDSConv1d(channels, kernel_size, dilation=1),
            DDSConv1d(channels, kernel_size, dilation=1),
            DDSConv1d(channels, kernel_size, dilation=1)
        ])

    def forward(self, x, x_mask=None):
        for c1, c2 in zip(self.convs1, self.convs2):
            xt = F.leaky_relu(x, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c1(xt)
            xt = F.leaky_relu(xt, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c2(xt)
            x = xt + x
        if x_mask is not None:
            x = x * x_mask
        return x

    def remove_weight_norm(self):
        for l in self.convs1:
            remove_weight_norm(l)
        for l in self.convs2:
            remove_weight_norm(l)


class ResBlock2(torch.nn.Module):
    def __init__(self, channels, kernel_size=3, dilation=(1, 3)):
        super(ResBlock2, self).__init__()
        self.convs = nn.ModuleList([
            weight_norm(Conv1d(channels, channels, kernel_size, 1, dilation=dilation[0],
                               padding=get_padding(kernel_size, dilation[0]))),
            weight_norm(Conv1d(channels, channels, kernel_size, 1, dilation=dilation[1],
                               padding=get_padding(kernel_size, dilation[1])))
        ])
        self.convs.apply(init_weights)

    def forward(self, x, x_mask=None):
        for c in self.convs:
            xt = F.leaky_relu(x, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c(xt)
            x = xt + x
        if x_mask is not None:
            x = x * x_mask
        return x

    def remove_weight_norm(self):
        for l in self.convs:
            remove_weight_norm(l)


class ResBlock3(torch.nn.Module):
    def __init__(self, channels, kernel_size=3, dilation=(1, 3)):
        super(ResBlock3, self).__init__()
        self.convs = nn.ModuleList([
            weight_norm(Conv1d(channels, channels, kernel_size, 1, dilation=dilation[0],
                               padding=get_padding(kernel_size, dilation[0])))
        ])
        self.convs.apply(init_weights)

    def forward(self, x, x_mask=None):
        for c in self.convs:
            xt = F.leaky_relu(x, LRELU_SLOPE)
            if x_mask is not None:
                xt = xt * x_mask
            xt = c(xt)
            x = xt + x
        if x_mask is not None:
            x = x * x_mask
        return x

    def remove_weight_norm(self):
        for l in self.convs:
            remove_weight_norm(l)


class Log(nn.Module):
  def forward(self, x, x_mask, reverse=False, **kwargs):
    if not reverse:
      y = torch.log(torch.clamp_min(x, 1e-5)) * x_mask
      logdet = torch.sum(-y, [1, 2])
      return y, logdet
    else:
      x = torch.exp(x) * x_mask
      return x
    

class Flip(nn.Module):
  def forward(self, x, *args, reverse=False, **kwargs):
    x = torch.flip(x, [1])
    if not reverse:
      logdet = torch.zeros(x.size(0)).to(dtype=x.dtype, device=x.device)
      return x, logdet
    else:
      return x


class ElementwiseAffine(nn.Module):
  def __init__(self, channels):
    super().__init__()
    self.channels = channels
    self.m = nn.Parameter(torch.zeros(channels,1))
    self.logs = nn.Parameter(torch.zeros(channels,1))

  def forward(self, x, x_mask, reverse=False, **kwargs):
    if not reverse:
      y = self.m + torch.exp(self.logs) * x
      y = y * x_mask
      logdet = torch.sum(self.logs * x_mask, [1,2])
      return y, logdet
    else:
      x = (x - self.m) * torch.exp(-self.logs) * x_mask
      return x


class ResidualCouplingLayer(nn.Module):
  def __init__(self,
      channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      p_dropout=0,
      gin_channels=0,
      mean_only=False):
    assert channels % 2 == 0, "channels should be divisible by 2"
    super().__init__()
    self.channels = channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.half_channels = channels // 2
    self.mean_only = mean_only

    self.pre = nn.Conv1d(self.half_channels, hidden_channels, 1)
    self.enc = WN(hidden_channels, kernel_size, dilation_rate, n_layers, p_dropout=p_dropout, gin_channels=gin_channels)
    self.post = nn.Conv1d(hidden_channels, self.half_channels * (2 - mean_only), 1)
    self.post.weight.data.zero_()
    self.post.bias.data.zero_()

  def forward(self, x, x_mask, g=None, reverse=False):
    x0, x1 = torch.split(x, [self.half_channels]*2, 1)
    h = self.pre(x0) * x_mask
    h = self.enc(h, x_mask, g=g)
    stats = self.post(h) * x_mask
    if not self.mean_only:
      m, logs = torch.split(stats, [self.half_channels]*2, 1)
    else:
      m = stats
      logs = torch.zeros_like(m)

    if not reverse:
      x1 = m + x1 * torch.exp(logs) * x_mask
      x = torch.cat([x0, x1], 1)
      logdet = torch.sum(logs, [1,2])
      return x, logdet
    else:
      x1 = (x1 - m) * torch.exp(-logs) * x_mask
      x = torch.cat([x0, x1], 1)
      return x


class ResidualCouplingContextLayer(nn.Module):
  def __init__(self,
      channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      p_dropout=0,
      gin_channels=0,
      mean_only=False):
    assert channels % 2 == 0, "channels should be divisible by 2"
    super().__init__()
    self.channels = channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.half_channels = channels // 2
    self.mean_only = mean_only

    self.pre = nn.Conv1d(self.half_channels + 3, hidden_channels, 1)
    self.enc = WN(hidden_channels, kernel_size, dilation_rate, n_layers, p_dropout=p_dropout, gin_channels=gin_channels)
    self.post = nn.Conv1d(hidden_channels, self.half_channels * (2 - mean_only), 1)
    self.post.weight.data.zero_()
    self.post.bias.data.zero_()

  def forward(self, x, x_mask, context, g=None, reverse=False):
    x0, x1 = torch.split(x, [self.half_channels]*2, 1)
    h = self.pre(torch.cat([x0, context], dim=1)) * x_mask
    h = self.enc(h, x_mask, g=g)
    stats = self.post(h) * x_mask
    if not self.mean_only:
      m, logs = torch.split(stats, [self.half_channels]*2, 1)
    else:
      m = stats
      logs = torch.zeros_like(m)

    if not reverse:
      x1 = m + x1 * torch.exp(logs) * x_mask
      x = torch.cat([x0, x1], 1)
      logdet = torch.sum(logs, [1,2])
      return x, logdet
    else:
      x1 = (x1 - m) * torch.exp(-logs) * x_mask
      x = torch.cat([x0, x1], 1)
      return x

class ResidualCouplingSelfAttentionLayer(nn.Module):
  def __init__(self,
      channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      p_dropout=0,
      gin_channels=0,
      mean_only=False):
    assert channels % 2 == 0, "channels should be divisible by 2"
    super().__init__()
    self.channels = channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.half_channels = channels // 2
    self.mean_only = mean_only

    self.pre = nn.Conv1d(self.half_channels, hidden_channels, 1)
    self.enc = attentions.FFT(hidden_channels, kernel_size, dilation_rate, n_layers=n_layers, p_dropout=p_dropout, gin_channels=gin_channels)
    # self.enc = WN(hidden_channels, kernel_size, dilation_rate, n_layers, p_dropout=p_dropout, gin_channels=gin_channels)
    self.post = nn.Conv1d(hidden_channels, self.half_channels * (2 - mean_only), 1)
    self.post.weight.data.zero_()
    self.post.bias.data.zero_()

  def forward(self, x, x_mask, g=None, reverse=False):
    x0, x1 = torch.split(x, [self.half_channels]*2, 1)
    h = self.pre(x0) * x_mask
    h = self.enc(h, x_mask)
    stats = self.post(h) * x_mask
    if not self.mean_only:
      m, logs = torch.split(stats, [self.half_channels]*2, 1)
    else:
      m = stats
      logs = torch.zeros_like(m)

    if not reverse:
      x1 = m + x1 * torch.exp(logs) * x_mask
      x = torch.cat([x0, x1], 1)
      logdet = torch.sum(logs, [1,2])
      return x, logdet
    else:
      x1 = (x1 - m) * torch.exp(-logs) * x_mask
      x = torch.cat([x0, x1], 1)
      return x


class ConvFlow(nn.Module):
  def __init__(self, in_channels, filter_channels, kernel_size, n_layers, num_bins=10, tail_bound=5.0):
    super().__init__()
    self.in_channels = in_channels
    self.filter_channels = filter_channels
    self.kernel_size = kernel_size
    self.n_layers = n_layers
    self.num_bins = num_bins
    self.tail_bound = tail_bound
    self.half_channels = in_channels // 2

    self.pre = nn.Conv1d(self.half_channels, filter_channels, 1)
    self.convs = DDSConv(filter_channels, kernel_size, n_layers, p_dropout=0.)
    self.proj = nn.Conv1d(filter_channels, self.half_channels * (num_bins * 3 - 1), 1)
    self.proj.weight.data.zero_()
    self.proj.bias.data.zero_()

  def forward(self, x, x_mask, g=None, reverse=False):
    x0, x1 = torch.split(x, [self.half_channels]*2, 1)
    h = self.pre(x0)
    h = self.convs(h, x_mask, g=g)
    h = self.proj(h) * x_mask

    b, c, t = x0.shape
    h = h.reshape(b, c, -1, t).permute(0, 1, 3, 2) # [b, cx?, t] -> [b, c, t, ?]

    unnormalized_widths = h[..., :self.num_bins] / math.sqrt(self.filter_channels)
    unnormalized_heights = h[..., self.num_bins:2*self.num_bins] / math.sqrt(self.filter_channels)
    unnormalized_derivatives = h[..., 2 * self.num_bins:]

    x1, logabsdet = piecewise_rational_quadratic_transform(x1,
        unnormalized_widths,
        unnormalized_heights,
        unnormalized_derivatives,
        inverse=reverse,
        tails='linear',
        tail_bound=self.tail_bound
    )

    x = torch.cat([x0, x1], 1) * x_mask
    logdet = torch.sum(logabsdet * x_mask, [1,2])
    if not reverse:
        return x, logdet
    else:
        return x

class Conv1d_v2(nn.Module):

  "Conv1d for spectral normalisation and orthogonal initialisation"

  def __init__(self,
          in_channels,
          out_channels,
          kernel_size=1,
          stride=1,
          dilation=1,
          groups=1,
          norm_type="weight_norm",
          init_type=None):

    super(Conv1d_v2, self).__init__()
    self.in_channels = in_channels
    self.out_channels = out_channels
    self.kernel_size = kernel_size
    self.stride = stride
    self.dilation = dilation
    self.groups = groups
    pad = dilation * (kernel_size - 1) // 2

    self.layer = nn.Conv1d(in_channels, out_channels, kernel_size=kernel_size,
            stride=stride, padding=pad, dilation=dilation, groups=groups)
    if init_type == "orthogonal":
        nn.init.orthogonal_(self.layer.weight)
    elif init_type == "normal":
        nn.init.normal_(self.layer.weight, mean=0.0, std=0.01)

    if norm_type == "weight_norm":
        self.layer = weight_norm(self.layer)
    elif norm_type == "spectral_norm":
        self.layer = spectral_norm(self.layer)

  def forward(self, inputs):
    return self.layer(inputs)


class ResStack(nn.Module):
  def __init__(self, channel, kernel_size=3, base=3, nums=4):
    super(ResStack, self).__init__()

    self.layers = nn.ModuleList([
      nn.Sequential(
          nn.LeakyReLU(),
          nn.utils.weight_norm(nn.Conv1d(channel, channel,
              kernel_size=kernel_size, dilation=base**i, padding=base**i)),
          nn.LeakyReLU(),
          nn.utils.weight_norm(nn.Conv1d(channel, channel,
              kernel_size=kernel_size, dilation=1, padding=1)),
      )
      for i in range(nums)
    ])

  def forward(self, x):
    for layer in self.layers:
      x = x + layer(x)
    return x


class Encoder(nn.Module):
  def __init__(self,
                in_channels,
                out_channels=513,
                base_channels=64,
                proj_kernel_size=3,
                stack_kernel_size=3,
                stack_dilation_base=2,
                stacks=6,
                channels=[64, 128, 128, 256, 512],
                down_sample_factors=[2, 4, 4, 8,]):
    super(Encoder, self).__init__()

    act_slope = 0.2
    layers = []
    # pre proj_layer
    layers += [
        Conv1d_v2(in_channels,
                base_channels,
                kernel_size=proj_kernel_size,
                stride=1),
        nn.LeakyReLU(act_slope, True)
    ]

    # channels: [512, 256, 128, 64], upsample_factors: [5, 2, 2]
    for (in_c, out_c), down_f in zip(
            zip(channels[:-1], channels[1:]), down_sample_factors):
        layers += [
            Conv1d_v2(in_c, out_c, kernel_size=down_f * 2, stride=down_f),
            ResStack(out_c, stack_kernel_size, stack_dilation_base, stacks),
            nn.LeakyReLU(act_slope, True)
        ]

    # post layers
    layers += [
        Conv1d_v2(channels[-1],
                out_channels,
                proj_kernel_size,
                stride=1),
        nn.Tanh()
    ]
    self.generator = nn.Sequential(*layers)

  def forward(self, conditions, z_inputs=None):
    return self.generator(conditions)


class LayerNorm_v2(torch.nn.LayerNorm):
    """Layer normalization module.

    :param int nout: output dim size
    :param int dim: dimension to be normalized
    """

    def __init__(self, nout, dim=-1):
        """Construct an LayerNorm object."""
        super(LayerNorm_v2, self).__init__(nout, eps=1e-12)
        self.dim = dim

    def forward(self, x):
        """Apply layer normalization.

        :param torch.Tensor x: input tensor
        :return: layer normalized tensor
        :rtype torch.Tensor
        """
        if self.dim == -1:
            return super(LayerNorm_v2, self).forward(x)
        return super(LayerNorm_v2, self).forward(x.transpose(1, -1)).transpose(1, -1)


class VariancePredictor(torch.nn.Module):
  """Variance predictor module.

  This is a module of variacne predictor described in `FastSpeech 2:
  Fast and High-Quality End-to-End Text to Speech`_.

  .. _`FastSpeech 2: Fast and High-Quality End-to-End Text to Speech`:
      https://arxiv.org/abs/2006.04558

  """

  def __init__(
      self,
      idim: int,
      n_layers: int = 2,
      n_chans: int = 384,
      kernel_size: int = 3,
      bias: bool = True,
      dropout_rate: float = 0.5,
  ):
    """Initilize duration predictor module.

    Args:
        idim (int): Input dimension.
        n_layers (int, optional): Number of convolutional layers.
        n_chans (int, optional): Number of channels of convolutional layers.
        kernel_size (int, optional): Kernel size of convolutional layers.
        dropout_rate (float, optional): Dropout rate.

    """
    super().__init__()
    self.conv = torch.nn.ModuleList()
    for idx in range(n_layers):
      in_chans = idim if idx == 0 else n_chans
      self.conv += [
        torch.nn.Sequential(
          torch.nn.Conv1d(
              in_chans,
              n_chans,
              kernel_size,
              stride=1,
              padding=(kernel_size - 1) // 2,
              bias=bias,
          ),
          torch.nn.ReLU(),
          LayerNorm_v2(n_chans, dim=1),
          torch.nn.Dropout(dropout_rate),
        )
      ]
    self.linear = torch.nn.Linear(n_chans, 1)

  def forward(self, xs: torch.Tensor, x_masks: torch.Tensor = None) -> torch.Tensor:
    """Calculate forward propagation.

    Args:
        xs (Tensor): Batch of input sequences (B, Tmax, idim).
        x_masks (ByteTensor, optional):
            Batch of masks indicating padded part (B, Tmax).

    Returns:
        Tensor: Batch of predicted sequences (B, Tmax, 1).

    """
    # xs = xs.transpose(1, -1)  # (B, idim, Tmax)
    for f in self.conv:
      xs = f(xs)  # (B, C, Tmax)

    xs = self.linear(xs.transpose(1, 2))  # (B, Tmax, 1)

    if x_masks is not None:
      xs = xs.masked_fill(x_masks, 0.0)

    return xs
        

class LF0Predictor(torch.nn.Module):
  """Variance predictor module.

  This is a module of variacne predictor described in `FastSpeech 2:
  Fast and High-Quality End-to-End Text to Speech`_.

  .. _`FastSpeech 2: Fast and High-Quality End-to-End Text to Speech`:
      https://arxiv.org/abs/2006.04558

  """

  def __init__(
      self,
      idim: int,
      n_layers: int = 2,
      n_chans: int = 384,
      kernel_size: int = 3,
      bias: bool = True,
      dropout_rate: float = 0.5,
  ):
    """Initilize duration predictor module.

    Args:
        idim (int): Input dimension.
        n_layers (int, optional): Number of convolutional layers.
        n_chans (int, optional): Number of channels of convolutional layers.
        kernel_size (int, optional): Kernel size of convolutional layers.
        dropout_rate (float, optional): Dropout rate.

    """
    super().__init__()
    self.conv = torch.nn.ModuleList()
    for idx in range(n_layers):
      in_chans = idim if idx == 0 else n_chans
      self.conv += [
        torch.nn.Sequential(
          torch.nn.Conv1d(
              in_chans,
              n_chans,
              kernel_size,
              stride=1,
              padding=(kernel_size - 1) // 2,
              bias=bias,
          ),
          torch.nn.ReLU(),
          LayerNorm_v2(n_chans, dim=1),
          torch.nn.Dropout(dropout_rate),
        )
      ]
    self.rnn = nn.LSTM(input_size=n_chans, hidden_size=n_chans,num_layers=2, bias=True, batch_first=False, dropout=0.5,bidirectional=True)
    self.linear = torch.nn.Linear(n_chans * 2, 1)

  def forward(self, xs: torch.Tensor, x_masks: torch.Tensor = None) -> torch.Tensor:
    """Calculate forward propagation.

    Args:
        xs (Tensor): Batch of input sequences (B, Tmax, idim).
        x_masks (ByteTensor, optional):
            Batch of masks indicating padded part (B, Tmax).

    Returns:
        Tensor: Batch of predicted sequences (B, Tmax, 1).

    """
    # xs = xs.transpose(1, -1)  # (B, idim, Tmax)
    for f in self.conv:
      xs = f(xs) * x_masks  # (B, C, Tmax)
    xs = xs.transpose(1, 2)
    xs, (hs, hc) = self.rnn(xs)
    # print(xs)
    # xs = torch.cat(xs, dim=2)
    xs = self.linear(xs)
    xs = xs.transpose(1, 2)
    # xs = self.linear(xs.transpose(1, 2))  # (B, Tmax, 1)

    # if x_masks is not None:
    #   xs = xs.masked_fill(x_masks, 0.0)

    return xs * x_masks
        

class PQMFConv(nn.Module):
    def __init__(self, kernel_size=7, padding=3):
        super(PQMFConv, self).__init__()
        self.pqmf_conv1 = weight_norm(
            Conv1d(1, 128, kernel_size, 1, padding=3))
        self.pqmf_conv2 = weight_norm(
            Conv1d(128, 1, kernel_size, 1, padding=3))
        self.pqmf_conv1.apply(init_weights)
        self.pqmf_conv2.apply(init_weights)

    def forward(self, inputs):
        outputs = self.pqmf_conv1(inputs)
        outputs = F.leaky_relu(outputs, 0.1)
        outputs = self.pqmf_conv2(outputs)
        outputs = torch.tanh(outputs)
        return outputs

    def remove_weight_norm(self):
        remove_weight_norm(self.pqmf_conv1)
        remove_weight_norm(self.pqmf_conv2)

