import matplotlib.pyplot as plt
import sys
import os
import json
import math
import torch
from torch import nn
from torch.nn import functional as F
from torch.utils.data import DataLoader

import commons
import utils
from models_newamp_exp16_4_hifi_band_48 import SynthesizerTrn
from models_newamp_exp16_4_hifi_band_48 import SynthesizerTrn_3k

from scipy.io.wavfile import write
from tqdm import tqdm
import numpy as np

from thop import profile, clever_format
import time
from scipy.interpolate import interp1d
from mel_processing import mel_spectrogram_torch_nhv2
#os.environ["CUDA_VISIBLE_DEVICES"] = ""
#torch.set_num_threads(1)
def interp1d_new(pitch):
    pitch = pitch.reshape(-1)
    nonzero_ids = np.where(pitch > 0)[0]
    if len(nonzero_ids) > 1:
        interp_fn = interp1d(
            nonzero_ids,
            pitch[nonzero_ids],
            fill_value=(pitch[nonzero_ids[0]], pitch[nonzero_ids[-1]]),
            bounds_error=False,
        )
        pitch = interp_fn(np.arange(0, len(pitch)))

    return pitch.reshape(-1, 1)
# LJspeech
hps = utils.get_hparams_from_file("./configs/bigdata_16to48k.json")
net_g_3k = SynthesizerTrn_3k(
    250,
    hps.data.filter_length // 2 + 1,
    hps.train.segment_size // hps.data.hop_length,
    **hps.model)
net_g = SynthesizerTrn(
    250,
    hps.data.filter_length // 2 + 1,
    hps.train.segment_size // hps.data.hop_length,
    **hps.model)
if torch.cuda.is_available():
    print("use gpu")
    device = torch.device('cuda')
    net_g.to(device)
    net_g_3k.to(device)
else:
    device = torch.device('cpu')
    print("use cpu")

_ = net_g_3k.eval()
_ = net_g.eval()
mel_dir = sys.argv[1]
pitch_dir = sys.argv[2]
out_dir = sys.argv[3]
model_name = sys.argv[4]
_ = utils.load_checkpoint("ckpts/G3k_600000.pth", net_g_3k, None)
_ = utils.load_checkpoint("ckpts/{}.pth".format(model_name), net_g, None)

#mel_list = os.listdir("huawei_test/mel")
mel_list = os.listdir(mel_dir)
print(mel_list)
for file_name in tqdm(mel_list):
    if not file_name.endswith(".npy"):
        print(file_name)
        continue
    #mel = np.load(os.path.join("huawei_test/mel", file_name))
    mel = np.load(os.path.join(mel_dir, file_name), allow_pickle=True)
    if mel.shape[0] != 80:
        mel = mel.transpose(1, 0) #/ 4
    LF0 = np.load(os.path.join(pitch_dir, file_name))
    #LF0 = np.load(os.path.join('huawei_test/lf0', file_name))
    uv = np.zeros(LF0.shape, dtype=np.float32)
    uv[LF0 > 0] = 1
    LF0 = np.where(LF0>0., np.exp(LF0), 0.)
    LF0 = interp1d_new(LF0)
    LF0 = LF0.reshape([1, -1])
    uv = uv.reshape([1, -1])

    with torch.no_grad():
        #print(file_name)
        mel = torch.from_numpy(mel).float().to(device)
        LF0 = torch.from_numpy(LF0).float().to(device)
        uv = torch.from_numpy(uv).float().to(device)
        mel = torch.unsqueeze(mel, 0)
        LF0 = torch.unsqueeze(LF0, 0)
        uv = torch.unsqueeze(uv, 0)
        if(LF0.shape[2] > mel.shape[2] and LF0.shape[2] - mel.shape[2] < 10):
            LF0 = LF0[:, :, :mel.shape[2]]
        if(LF0.shape[2] < mel.shape[2]):
            mel = mel[:, :, :LF0.shape[2]]

        #torch.set_num_threads(1)
        start_time = time.time()
        prior_audio_6k, harmonic_noise, uv_upsample = net_g_3k.infer(mel, LF0, uv)
        nhv_mel,res_mel = mel_spectrogram_torch_nhv2(mel, prior_audio_6k.squeeze(1), 1024, hps.data.n_mel_channels, 16000, 200, 800, 0, 8000, hps.data.min_db, hps.data.max_abs_value, hps.data.min_level_db, hps.data.ref_level_db)
        audio_6k = prior_audio_6k.squeeze().data.cpu().float().numpy()
        #harmonic = harmonic_noise[:, 0, :].squeeze().data.cpu().float().numpy()
        #noise = harmonic_noise[:, 1, :].squeeze().data.cpu().float().numpy()
        sin = uv_upsample.squeeze().data.cpu().float().numpy()
        prior_audio = net_g(nhv_mel, res_mel, mel, uv_upsample)
        end_time = time.time()
        #print("RTF: ", (end_time-start_time) / o[0,0].data.float().numpy().shape[0] * 3000.)

        audio = prior_audio.squeeze().data.cpu().float().numpy()
        # audio = net_g.infer(x_tst, x_tst_lengths, noise_scale=.667, noise_scale_w=0.8, length_scale=1)[0][0,0].data.cpu().float().numpy()

    # 保存wav
    # audio = audio[0, :]
    audio = audio * hps.data.max_wav_value
    audio = audio.astype(np.int16)
    audio_6k = audio_6k * hps.data.max_wav_value
    audio_6k = audio_6k.astype(np.int16)
    #harmonic = harmonic * hps.data.max_wav_value
    #harmonic = harmonic.astype(np.int16)
    #noise = noise * hps.data.max_wav_value
    #noise = noise.astype(np.int16)
    # print("audio : ", audio[:20], audio.shape, audio.dtype, np.max(audio), np.min(audio))
    os.makedirs(out_dir, exist_ok=True)
    write(os.path.join(out_dir, file_name[:-4]+".wav" ), 48000, audio)
    #write(os.path.join(out_dir, '6k_' + file_name.split('.')[0] + '.wav'), 24000, audio_6k)
    #write(os.path.join(out_dir, 'har_' + file_name.split('.')[0] + '.wav'), 24000, harmonic)
    #write(os.path.join(out_dir, 'noi_' + file_name.split('.')[0] + '.wav'), 24000, noise)
    #np.save(os.path.join(out_dir, 'sin_' + file_name.split('.')[0] + '.npy'), sin)
# #VCTK
# hps = utils.get_hparams_from_file("./configs/vctk_base.json")
# net_g = SynthesizerTrn(
#     len(symbols),
#     hps.data.filter_length // 2 + 1,
#     hps.train.segment_size // hps.data.hop_length,
#     n_speakers=hps.data.n_speakers,
#     **hps.model).cuda()
# _ = net_g.eval()
# _ = utils.load_checkpoint("/path/to/pretrained_vctk.pth", net_g, None)

# stn_tst = get_text("VITS is Awesome!", hps)
# with torch.no_grad():
#     x_tst = stn_tst.cuda().unsqueeze(0)
#     x_tst_lengths = torch.LongTensor([stn_tst.size(0)]).cuda()
#     sid = torch.LongTensor([4]).cuda()
#     audio = net_g.infer(x_tst, x_tst_lengths, sid=sid, noise_scale=.667, noise_scale_w=0.8, length_scale=1)[0][0,0].data.cpu().float().numpy()
# ipd.display(ipd.Audio(audio, rate=hps.data.sampling_rate, normalize=False))

# #Voice Conversion
# dataset = TextAudioSpeakerLoader(hps.data.validation_files, hps.data)
# collate_fn = TextAudioSpeakerCollate()
# loader = DataLoader(dataset, num_workers=8, shuffle=False,
#     batch_size=1, pin_memory=True,
#     drop_last=True, collate_fn=collate_fn)
# data_list = list(loader)

# with torch.no_grad():
#     x, x_lengths, spec, spec_lengths, y, y_lengths, sid_src = [x.cuda() for x in data_list[0]]
#     sid_tgt1 = torch.LongTensor([1]).cuda()
#     sid_tgt2 = torch.LongTensor([2]).cuda()
#     sid_tgt3 = torch.LongTensor([4]).cuda()
#     audio1 = net_g.voice_conversion(spec, spec_lengths, sid_src=sid_src, sid_tgt=sid_tgt1)[0][0,0].data.cpu().float().numpy()
#     audio2 = net_g.voice_conversion(spec, spec_lengths, sid_src=sid_src, sid_tgt=sid_tgt2)[0][0,0].data.cpu().float().numpy()
#     audio3 = net_g.voice_conversion(spec, spec_lengths, sid_src=sid_src, sid_tgt=sid_tgt3)[0][0,0].data.cpu().float().numpy()
# print("Original SID: %d" % sid_src.item())
# ipd.display(ipd.Audio(y[0].cpu().numpy(), rate=hps.data.sampling_rate, normalize=False))
# print("Converted SID: %d" % sid_tgt1.item())
# ipd.display(ipd.Audio(audio1, rate=hps.data.sampling_rate, normalize=False))
# print("Converted SID: %d" % sid_tgt2.item())
# ipd.display(ipd.Audio(audio2, rate=hps.data.sampling_rate, normalize=False))
# print("Converted SID: %d" % sid_tgt3.item())
# ipd.display(ipd.Audio(audio3, rate=hps.data.sampling_rate, normalize=False))


