import copy
import math
import torch
from torch import nn
from torch.nn import functional as F
import numpy as np

import dspgan.nhv.commons
import dspgan.nhv.modules
import dspgan.nhv.attentions
from dspgan.nhv.frequency_discriminator_v4 import FrequencyDiscriminatorV4
from torch.nn import Conv1d, ConvTranspose1d, AvgPool1d, Conv2d
from torch.nn.utils import weight_norm, remove_weight_norm, spectral_norm
from dspgan.nhv.commons import init_weights, get_padding
# from text.symbols import symbols, tone_set, seg_tag_set, prosody_set
from dspgan.nhv.mel_processing import mel_spectrogram_torch, spec_to_mel_torch, spectrogram_torch

from scipy import interpolate
from dspgan.nhv.ops.stft import *

from dspgan.nhv.nhv.model_newamp_exp16_4_hifi_48k import NeuralHomomorphicVocoder, Melgan

class StochasticDurationPredictor(nn.Module):
  def __init__(self, in_channels, filter_channels, kernel_size, p_dropout, n_flows=4, gin_channels=0):
    super().__init__()
    filter_channels = in_channels # it needs to be removed from future version.
    self.in_channels = in_channels
    self.filter_channels = filter_channels
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout
    self.n_flows = n_flows
    self.gin_channels = gin_channels

    self.log_flow = modules.Log()
    self.flows = nn.ModuleList()
    self.flows.append(modules.ElementwiseAffine(2))
    for i in range(n_flows):
      self.flows.append(modules.ConvFlow(2, filter_channels, kernel_size, n_layers=3))
      self.flows.append(modules.Flip())

    self.post_pre = nn.Conv1d(1, filter_channels, 1)
    self.post_proj = nn.Conv1d(filter_channels, filter_channels, 1)
    self.post_convs = modules.DDSConv(filter_channels, kernel_size, n_layers=3, p_dropout=p_dropout)
    self.post_flows = nn.ModuleList()
    self.post_flows.append(modules.ElementwiseAffine(2))
    for i in range(4):
      self.post_flows.append(modules.ConvFlow(2, filter_channels, kernel_size, n_layers=3))
      self.post_flows.append(modules.Flip())

    self.pre = nn.Conv1d(in_channels, filter_channels, 1)
    self.proj = nn.Conv1d(filter_channels, filter_channels, 1)
    self.convs = modules.DDSConv(filter_channels, kernel_size, n_layers=3, p_dropout=p_dropout)
    if gin_channels != 0:
      self.cond = nn.Conv1d(gin_channels, filter_channels, 1)

  def forward(self, x, x_mask, w=None, g=None, reverse=False, noise_scale=1.0):
    x = torch.detach(x)
    x = self.pre(x)
    if g is not None:
      g = torch.detach(g)
      x = x + self.cond(g)
    x = self.convs(x, x_mask)
    x = self.proj(x) * x_mask

    if not reverse:
      flows = self.flows
      assert w is not None

      logdet_tot_q = 0
      h_w = self.post_pre(w)
      h_w = self.post_convs(h_w, x_mask)
      h_w = self.post_proj(h_w) * x_mask
      e_q = torch.randn(w.size(0), 2, w.size(2)).to(device=x.device, dtype=x.dtype) * x_mask
      z_q = e_q
      for flow in self.post_flows:
        z_q, logdet_q = flow(z_q, x_mask, g=(x + h_w))
        logdet_tot_q += logdet_q
      z_u, z1 = torch.split(z_q, [1, 1], 1)
      u = torch.sigmoid(z_u) * x_mask
      z0 = (w - u) * x_mask
      logdet_tot_q += torch.sum((F.logsigmoid(z_u) + F.logsigmoid(-z_u)) * x_mask, [1,2])
      logq = torch.sum(-0.5 * (math.log(2*math.pi) + (e_q**2)) * x_mask, [1,2]) - logdet_tot_q

      logdet_tot = 0
      z0, logdet = self.log_flow(z0, x_mask)
      logdet_tot += logdet
      z = torch.cat([z0, z1], 1)
      for flow in flows:
        z, logdet = flow(z, x_mask, g=x, reverse=reverse)
        logdet_tot = logdet_tot + logdet
      nll = torch.sum(0.5 * (math.log(2*math.pi) + (z**2)) * x_mask, [1,2]) - logdet_tot
      return nll + logq # [b]
    else:
      flows = list(reversed(self.flows))
      flows = flows[:-2] + [flows[-1]] # remove a useless vflow
      z = torch.randn(x.size(0), 2, x.size(2)).to(device=x.device, dtype=x.dtype) * noise_scale
      for flow in flows:
        z = flow(z, x_mask, g=x, reverse=reverse)
      z0, z1 = torch.split(z, [1, 1], 1)
      logw = z0
      return logw


class DurationPredictor(nn.Module):
  def __init__(self, in_channels, filter_channels, kernel_size, p_dropout, gin_channels=0):
    super().__init__()

    self.in_channels = in_channels
    self.filter_channels = filter_channels
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout
    self.gin_channels = gin_channels

    self.drop = nn.Dropout(p_dropout)
    self.conv_1 = nn.Conv1d(in_channels, filter_channels, kernel_size, padding=kernel_size//2)
    self.norm_1 = modules.LayerNorm(filter_channels)
    self.conv_2 = nn.Conv1d(filter_channels, filter_channels, kernel_size, padding=kernel_size//2)
    self.norm_2 = modules.LayerNorm(filter_channels)
    self.conv_3 = nn.Conv1d(filter_channels, filter_channels, kernel_size, padding=kernel_size//2)
    self.norm_3 = modules.LayerNorm(filter_channels)
    self.proj = nn.Conv1d(filter_channels, 1, 1)

    if gin_channels != 0:
      self.cond = nn.Conv1d(gin_channels, in_channels, 1)

  def forward(self, x, x_mask, g=None):
    # x = torch.detach(x)
    if g is not None:
      g = torch.detach(g)
      x = x + self.cond(g)

    x = self.conv_1(x * x_mask)
    x = torch.relu(x)
    x = self.norm_1(x)
    x = self.drop(x)

    x = self.conv_2(x * x_mask)
    x = torch.relu(x)
    x = self.norm_2(x)
    x = self.drop(x)

    x = self.conv_3(x * x_mask)
    x = torch.relu(x)
    x = self.norm_3(x)
    x = self.drop(x)

    x = self.proj(x * x_mask)
    return x * x_mask


class TextEncoder(nn.Module):
  def __init__(self,
      n_vocab,
      out_channels,
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout):
    super().__init__()
    self.n_vocab = n_vocab
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout

    self.emb_phone = nn.Embedding(n_vocab, 384)
    nn.init.normal_(self.emb_phone.weight, 0.0, 384**-0.5)

    self.emb_tone = nn.Embedding(len(tone_set), 64)
    nn.init.normal_(self.emb_tone.weight, 0.0, 64**-0.5)

    self.emb_seg_tag = nn.Embedding(len(seg_tag_set), 32)
    nn.init.normal_(self.emb_seg_tag.weight, 0.0, 32**-0.5)

    self.emb_prosody = nn.Embedding(len(prosody_set), 32)
    nn.init.normal_(self.emb_prosody.weight, 0.0, 32**-0.5)

    self.pre_net = torch.nn.Linear(512, 192)
    self.pre_dur_net = torch.nn.Linear(512, 256)
    # self.pre_lf0_net = torch.nn.Linear(512, 192)

    # hidden_channels,
    self.encoder = attentions.Encoder(
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout)
    # self.proj= nn.Conv1d(hidden_channels, out_channels * 2, 1)

  def forward(self, phone, phone_lengths, tone, seg_tag, prosody):

    phone = self.emb_phone(phone) * math.sqrt(384) # [b, t, h]
    tone = self.emb_tone(tone) * math.sqrt(64) # [b, t, h]
    seg_tag = self.emb_seg_tag(seg_tag) * math.sqrt(32) # [b, t, h]
    prosody = self.emb_prosody(prosody) * math.sqrt(32) # [b, t, h]
    x = torch.cat([phone, tone, seg_tag, prosody], dim=-1)

    dur_input = self.pre_dur_net(x)
    dur_input = torch.transpose(dur_input, 1, -1)

    # lf0_input = self.pre_lf0_net(x)
    # lf0_input = torch.transpose(lf0_input, 1, -1)

    x = self.pre_net(x)

    x = torch.transpose(x, 1, -1) # [b, h, t]
    x_mask = torch.unsqueeze(commons.sequence_mask(phone_lengths, x.size(2)), 1).to(x.dtype)

    x = self.encoder(x * x_mask, x_mask)
    #stats = self.proj(x) * x_mask

    #m, logs = torch.split(stats, self.out_channels, dim=1)
    m, logs = None, None

    return x, m, logs, x_mask, dur_input


def pad_v2(input_ele, mel_max_length=None):
    if mel_max_length:
        max_len = mel_max_length
    else:
        max_len = max([input_ele[i].size(0) for i in range(len(input_ele))])

    out_list = list()
    for i, batch in enumerate(input_ele):
        if len(batch.shape) == 1:
            one_batch_padded = F.pad(
                batch, (0, max_len - batch.size(0)), "constant", 0.0
            )
        elif len(batch.shape) == 2:
            one_batch_padded = F.pad(
                batch, (0, 0, 0, max_len - batch.size(0)), "constant", 0.0
            )
        out_list.append(one_batch_padded)
    out_padded = torch.stack(out_list)
    return out_padded


class LengthRegulator(nn.Module):
    """ Length Regulator """

    def __init__(self):
      super(LengthRegulator, self).__init__()

    def LR(self, x, duration, max_len):
      x = torch.transpose(x, 1, 2)
      output = list()
      mel_len = list()
      for batch, expand_target in zip(x, duration):
        # print('LR 1 : ', batch.shape, expand_target.shape)
        expanded = self.expand(batch, expand_target)
        output.append(expanded)
        mel_len.append(expanded.shape[0])

      if max_len is not None:
        output = pad_v2(output, max_len)
      else:
        output = pad_v2(output)
      output = torch.transpose(output, 1, 2)
      return output, torch.LongTensor(mel_len)

    def expand(self, batch, predicted):
      predicted = torch.squeeze(predicted)
      out = list()

      for i, vec in enumerate(batch):
        # print('LR 2 : ', i, vec.shape, predicted.shape)
        expand_size = predicted[i].item()
        state_info_index = torch.unsqueeze(torch.arange(0, expand_size), 1).float()
        state_info_length = torch.unsqueeze(torch.Tensor([expand_size] * expand_size), 1).float()
        state_info = torch.cat([state_info_index, state_info_length], 1).to(vec.device)
        new_vec = vec.expand(max(int(expand_size), 0), -1)
        new_vec = torch.cat([new_vec, state_info], 1)
        out.append(new_vec)

        # out.append(vec.expand(max(int(expand_size), 0), -1))
      # print("LR 3 : ", out[0].shape)
      out = torch.cat(out, 0)

      return out

    def forward(self, x, duration, max_len):
      output, mel_len = self.LR(x, duration, max_len)
      return output, mel_len

class LF0Decoder(nn.Module):
  def __init__(self,
      out_LF0_channels,
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout,
      gin_channels=0):
    super().__init__()
    self.out_LF0_channels = out_LF0_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout
    self.gin_channels = gin_channels

    self.prenet = nn.Conv1d(hidden_channels + 2, hidden_channels, 3, padding=1)
    self.decoder = attentions.Decoder(
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout)
    self.proj = nn.Conv1d(hidden_channels, out_LF0_channels, 1)

    if gin_channels != 0:
      self.cond = nn.Conv1d(gin_channels, hidden_channels, 1)

  def forward(self, x, x_lengths, h, h_lengths, g=None):
    # x = self.emb(x) * math.sqrt(self.hidden_channels) # [b, t, h]
    # x = torch.transpose(x, 1, -1) # [b, h, t]
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    h_mask = torch.unsqueeze(commons.sequence_mask(h_lengths, h.size(2)), 1).to(h.dtype)
    x = self.prenet(x) * x_mask
    if(g is not None):
      x = x + self.cond(g)
    x = self.decoder(x * x_mask, x_mask, h * h_mask, h_mask)
    lf0 = self.proj(x) * x_mask

    return lf0, x_mask


class Decoder(nn.Module):
  def __init__(self,
      out_channels,
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout):
    super().__init__()
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout

    self.prenet = nn.Conv1d(hidden_channels + 2, hidden_channels, 3, padding=1)
    self.decoder = attentions.Decoder(
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout)
    self.proj= nn.Conv1d(hidden_channels, out_channels * 2, 1)

  def forward(self, x, x_lengths, h, h_lengths):
    # x = self.emb(x) * math.sqrt(self.hidden_channels) # [b, t, h]
    # x = torch.transpose(x, 1, -1) # [b, h, t]
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    h_mask = torch.unsqueeze(commons.sequence_mask(h_lengths, h.size(2)), 1).to(h.dtype)
    x = self.prenet(x) * x_mask
    x = self.decoder(x * x_mask, x_mask, h * h_mask, h_mask)
    stats = self.proj(x) * x_mask

    m, logs = torch.split(stats, self.out_channels, dim=1)
    x = (m + torch.randn_like(m) * torch.exp(logs)) * x_mask
    return x, m, logs, x_mask


class Decoder_conv1d(nn.Module):
  def __init__(self,
      out_channels,
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout,
      gin_channels=0):
    super().__init__()
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout

    self.prenet = nn.Conv1d(hidden_channels + hidden_channels + 2, hidden_channels, 3, padding=1)
    self.decoder = modules.ConvReluNorm(hidden_channels, hidden_channels, hidden_channels, kernel_size=3, n_layers=4, p_dropout=0.5)
    self.proj= nn.Conv1d(hidden_channels, out_channels * 2, 1)
    if gin_channels != 0:
      self.gin_channels = gin_channels

  def forward(self, x, x_lengths, h, h_lengths, g=None):
    # x = self.emb(x) * math.sqrt(self.hidden_channels) # [b, t, h]
    # x = torch.transpose(x, 1, -1) # [b, h, t]
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    h_mask = torch.unsqueeze(commons.sequence_mask(h_lengths, h.size(2)), 1).to(h.dtype)

    x = self.prenet(x) * x_mask
    if g is not None:
      x = x + self.cond(g)

    x = self.decoder(x * x_mask, x_mask)
    stats = self.proj(x) * x_mask

    m, logs = torch.split(stats, self.out_channels, dim=1)

    x = (m + torch.randn_like(m) * torch.exp(logs)) * x_mask
    return x, m, logs, x_mask


class Decoder_ResNet(nn.Module):
  def __init__(self,
      out_channels,
      hidden_channels,
      filter_channels,
      n_heads,
      n_layers,
      kernel_size,
      p_dropout):
    super().__init__()
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout

    self.prenet = nn.Conv1d(hidden_channels + 3, hidden_channels, 3, padding=1)
    self.prenet_res = nn.Conv1d(hidden_channels + 3, hidden_channels, 3, padding=1)
    #self.decoder_1 = attentions.Decoder(
    #  hidden_channels,
    #  filter_channels,
    #  n_heads,
    #  2,
    #  kernel_size,
    #  p_dropout)
    self.rnn = nn.LSTM(input_size=hidden_channels, hidden_size=hidden_channels, num_layers=2, bias=True, batch_first=True, dropout=0.5, bidirectional=False)
    self.decoder = modules.ConvReluNorm(hidden_channels, hidden_channels, hidden_channels, kernel_size=3, n_layers=6, p_dropout=0.5)
    self.proj_res = nn.Conv1d(hidden_channels, out_channels * 12, 1)
    self.proj = nn.Conv1d(hidden_channels, out_channels * 2, 1)

  def forward(self, x, x_lengths, target_z, is_training=False):
    # x = self.emb(x) * math.sqrt(self.hidden_channels) # [b, t, h]
    # x = torch.transpose(x, 1, -1) # [b, h, t]
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    # h_mask = torch.unsqueeze(commons.sequence_mask(h_lengths, h.size(2)), 1).to(h.dtype)

    # 两层LSTM + 4层Conv1d
    res_x = x
    res_x = self.prenet_res(res_x) * x_mask
    res_x = res_x.transpose(1, 2)
    res_x, (hs, hc) = self.rnn(res_x)
    res_x = res_x.transpose(1, 2)
    res_x = self.decoder(res_x * x_mask, x_mask)
    res_x = self.proj_res(res_x)

    # MDN
    res_x_mask = torch.unsqueeze(x_mask, 2)
    res_m, res_logs, res_weight = torch.split(res_x, self.out_channels * 4, dim=1)
    batch_size = res_m.size(0)
    res_m = res_m.reshape(batch_size, self.out_channels, 4, -1) * res_x_mask
    res_logs = res_logs.reshape(batch_size, self.out_channels, 4, -1) * res_x_mask
    res_weight = res_weight.reshape(batch_size, self.out_channels, 4, -1) * res_x_mask
    res_weight = torch.nn.Softmax(dim=2)(res_weight)
    res_x = (res_m + torch.randn_like(res_m) * torch.exp(res_logs) * 0.8) * res_x_mask
    res_x = res_x * res_weight * res_x_mask
    res_x = torch.sum(res_x, dim=2)

    # 计算Loss
    if(is_training):
      target_z = torch.detach(target_z)
      std = torch.exp(res_logs)
      mean = res_m
      factors = 1 / math.sqrt(2 * math.pi) / std * res_x_mask
      target_z = torch.unsqueeze(target_z, 2)
      exponent = torch.exp(-1 / 2 * torch.square((target_z - mean) / std)) * res_x_mask

      # 混合高斯模型下的似然函数(取值为y)
      # print("GMM shape: ", res_weight[0,:,:,0], factors[0,:,:,0], exponent[0,:,:,0])
      GMM_likelihood = torch.sum(res_weight * factors * exponent, dim=2)
      # print(GMM_likelihood.max(), GMM_likelihood.min(), res_weight[0,0,:,0], GMM_likelihood.shape)
      # 加负号: 梯度下降 ==> 似然函数最大
      log_likelihood = - torch.log(torch.clamp(GMM_likelihood, min=1e-8)) * x_mask
      log_likelihood = torch.mean(log_likelihood)
      # print(log_likelihood)
    else:
      log_likelihood = 0.

    # 残差部分
    x = self.prenet(x) * x_mask
    stats = self.proj(x) * x_mask
    m, logs = torch.split(stats, self.out_channels, dim=1)
    x = (m + torch.randn_like(m) * torch.exp(logs)) * x_mask
    return x, m, logs, x_mask, res_x, log_likelihood



class ResidualCouplingBlock(nn.Module):
  def __init__(self,
      channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      n_flows=4,
      gin_channels=0):
    super().__init__()
    self.channels = channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.n_flows = n_flows
    self.gin_channels = gin_channels

    self.flows = nn.ModuleList()
    for i in range(n_flows):
      self.flows.append(modules.ResidualCouplingLayer(channels, hidden_channels, kernel_size, dilation_rate, n_layers, gin_channels=gin_channels, mean_only=True))
      self.flows.append(modules.Flip())

  def forward(self, x, x_mask, g=None, reverse=False):
    if not reverse:
      for flow in self.flows:
        x, _ = flow(x, x_mask, g=g, reverse=reverse)
    else:
      for flow in reversed(self.flows):
        x = flow(x, x_mask, g=g, reverse=reverse)
    return x


class ResidualCouplingFeatureMapBlock(nn.Module):
  def __init__(self,
      channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      n_flows=4,
      gin_channels=0):
    super().__init__()
    self.channels = channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.n_flows = n_flows
    self.gin_channels = gin_channels

    self.flows = nn.ModuleList()
    self.flips = nn.ModuleList()
    for i in range(n_flows):
      self.flows.append(modules.ResidualCouplingLayer(channels, hidden_channels, kernel_size, dilation_rate, n_layers, gin_channels=gin_channels, mean_only=True))
      self.flips.append(modules.Flip())

  def forward(self, x, x_mask, g=None, reverse=False):
    Feature_Maps = []
    if not reverse:
      for i in range(len(self.flows)):
        x, _ = self.flows[i](x, x_mask, g=g, reverse=reverse)
        x, _ = self.flips[i](x, x_mask, g=g, reverse=reverse)
        Feature_Maps.append(x)
    else:
      for i in reversed(range(len(self.flows))):
        x = self.flows[i](x, x_mask, g=g, reverse=reverse)
        x = self.flips[i](x, x_mask, g=g, reverse=reverse)
        Feature_Maps.append(x)
      Feature_Maps = reversed(Feature_Maps)
    return x, Feature_Maps


class PosteriorEncoder(nn.Module):
  def __init__(self,
      in_channels,
      out_channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      gin_channels=0):
    super().__init__()
    self.in_channels = in_channels
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.gin_channels = gin_channels

    self.pre = nn.Conv1d(in_channels, hidden_channels, 1)
    self.enc = modules.WN(hidden_channels, kernel_size, dilation_rate, n_layers, gin_channels=gin_channels)
    self.proj = nn.Conv1d(hidden_channels, out_channels * 2, 1)

  def forward(self, x, x_lengths, g=None):
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    x = self.pre(x) * x_mask
    x = self.enc(x, x_mask, g=g)
    stats = self.proj(x) * x_mask
    m, logs = torch.split(stats, self.out_channels, dim=1)
    z = (m + torch.randn_like(m) * torch.exp(logs)) * x_mask
    return z, m, logs, x_mask


class PosteriorLF0Encoder(nn.Module):
  def __init__(self,
      in_channels,
      out_channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      gin_channels=0):
    super().__init__()
    self.in_channels = in_channels
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.gin_channels = gin_channels

    self.pre = nn.Conv1d(in_channels, hidden_channels, 1)
    self.conv_layers = nn.ModuleList()
    self.norm_layers = nn.ModuleList()
    self.relu_drop = nn.ReLU()
    for _ in range(n_layers):
      self.conv_layers.append(nn.Conv1d(hidden_channels, hidden_channels, kernel_size, padding=kernel_size//2))
      self.norm_layers.append(modules.LayerNorm(hidden_channels))
    self.proj = nn.Conv1d(hidden_channels, out_channels, 1)

  def forward(self, x, x_lengths, g=None):
    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    x = self.pre(x) * x_mask
    for i in range(self.n_layers):
      x = self.conv_layers[i](x * x_mask)
      x = self.norm_layers[i](x)
      x = self.relu_drop(x)
    x = self.proj(x) * x_mask
    return x



class PosteriorWavEncoder(nn.Module):
  def __init__(self,
      in_channels,
      out_channels,
      hidden_channels,
      kernel_size,
      dilation_rate,
      n_layers,
      gin_channels=0):
    super().__init__()
    self.in_channels = in_channels
    self.out_channels = out_channels
    self.hidden_channels = hidden_channels
    self.kernel_size = kernel_size
    self.dilation_rate = dilation_rate
    self.n_layers = n_layers
    self.gin_channels = gin_channels

    self.wav_enc = modules.Encoder(in_channels=1)

    self.pre = nn.Conv1d(in_channels, hidden_channels, 1)
    self.enc = modules.WN(hidden_channels, kernel_size, dilation_rate, n_layers, gin_channels=gin_channels)
    self.proj = nn.Conv1d(hidden_channels, out_channels * 2, 1)


  def forward(self, x, x_lengths, s, s_lengths, g=None):
    spc = self.wav_enc(s)
    x_lengths = s_lengths // 256

    x_mask = torch.unsqueeze(commons.sequence_mask(x_lengths, x.size(2)), 1).to(x.dtype)
    spc = spc * x_mask
    x = spc

    x = self.pre(x) * x_mask
    x = self.enc(x, x_mask, g=g)
    stats = self.proj(x) * x_mask
    m, logs = torch.split(stats, self.out_channels, dim=1)
    z = (m + torch.randn_like(m) * torch.exp(logs)) * x_mask
    return z, m, logs, x_mask

class AFL(torch.nn.Module):
    def __init__(self, spectrum_info_channel, source_excitation_channel, dilation, kernel_size=5):
      super(AFL, self).__init__()
      self.source_excitation_conv = weight_norm(Conv1d(source_excitation_channel, source_excitation_channel, 5, 1, dilation=dilation,
                               padding=get_padding(kernel_size, dilation)))
      self.spectrum_info_conv_1 = weight_norm(Conv1d(spectrum_info_channel, spectrum_info_channel, 5, 1,
                               padding=kernel_size//2))
      self.spectrum_info_conv_2 = weight_norm(Conv1d(spectrum_info_channel, spectrum_info_channel, 5, 1,
                               padding=kernel_size//2))

    def forward(self, spectrum_info, source_excitation):
      # print("AFL shape: ", spectrum_info.shape, source_excitation.shape)
      spectrum_info_alpha = self.spectrum_info_conv_1(spectrum_info)
      spectrum_info_beta = self.spectrum_info_conv_2(spectrum_info)
      source_excitation = self.source_excitation_conv(source_excitation)
      outputs = torch.tanh(source_excitation + spectrum_info_alpha) * \
                torch.sigmoid(source_excitation + spectrum_info_beta)
      return outputs

class AdaIN(torch.nn.Module):
    def __init__(self, in_channel):
      super(AdaIN, self).__init__()
      self.weight = nn.Parameter(torch.randn(1, in_channel, 1), requires_grad=True)
      self.weight.data.normal_(0.0, 0.01)

    def forward(self, x):
      # print(self.weight)
      gaussian_noise = torch.randn(x.shape).to(x.device)
      return x + self.weight * gaussian_noise

class Generator(torch.nn.Module):
    def __init__(self, initial_channel, resblock, resblock_kernel_sizes, resblock_dilation_sizes, upsample_rates, upsample_initial_channel, upsample_kernel_sizes, gin_channels=0):
        super(Generator, self).__init__()
        self.num_kernels = len(resblock_kernel_sizes)
        self.num_upsamples = len(upsample_rates)
        self.conv_pre = Conv1d(initial_channel + 32, upsample_initial_channel, 7, 1, padding=3)
        self.upsample_rates = upsample_rates

        resblock = modules.ResBlock1 if resblock == '1' else modules.ResBlock2

        # Speech Template
        self.conv_process_se = Conv1d(1, 32, 1, 1, bias=False)
        self.downs = nn.ModuleList()
        for i, (u, k) in enumerate(zip(upsample_rates, upsample_kernel_sizes)):
            i = len(upsample_rates) - 1 - i
            u = upsample_rates[i]
            k = upsample_kernel_sizes[i]
            self.downs.append(weight_norm(
                Conv1d(32, 32,
                       k, u, padding=k//2)))

        self.resblocks_downs = nn.ModuleList()
        for i in range(len(self.downs)):
            self.resblocks_downs.append(resblock(32, 7))

        self.concat_conv = nn.ModuleList()
        for i in range(len(upsample_rates)):
          ch = upsample_initial_channel//(2**(i+1))
          self.concat_conv.append(Conv1d(ch + 32, ch, 3, 1, padding=1, bias=False))

        self.ups = nn.ModuleList()
        for i, (u, k) in enumerate(zip(upsample_rates, upsample_kernel_sizes)):
            self.ups.append(weight_norm(
                ConvTranspose1d(upsample_initial_channel//(2**i), upsample_initial_channel//(2**(i+1)),
                                k, u, padding=(k-u)//2)))

        self.resblocks = nn.ModuleList()
        for i in range(len(self.ups)):
            ch = upsample_initial_channel//(2**(i+1))
            for j, (k, d) in enumerate(zip(resblock_kernel_sizes, resblock_dilation_sizes)):
                self.resblocks.append(resblock(ch, k, d))

        self.AdaINs_before = nn.ModuleList()
        self.AdaINs_after = nn.ModuleList()
        for i in range(len(self.ups)):
            ch = upsample_initial_channel//(2**(i+1))
            for j, (k, d) in enumerate(zip(resblock_kernel_sizes, resblock_dilation_sizes)):
                self.AdaINs_before.append(AdaIN(ch))
                self.AdaINs_after.append(AdaIN(ch))
        # self.source_excitation_channel = 32
        # self.AFLs = nn.ModuleList()
        # for dilation in [1, 2, 4, 8, 16, 32, 64, 128]:
        #   self.AFLs.append(AFL(ch, self.source_excitation_channel, dilation))
        # self.conv_source_excitation_post = Conv1d(self.source_excitation_channel, ch, 1, 1, bias=False)

        self.conv_post = Conv1d(ch, 1, 7, 1, padding=3, bias=False)
        self.ups.apply(init_weights)

        if gin_channels != 0:
            self.cond = nn.Conv1d(gin_channels, upsample_initial_channel, 1)

    def forward(self, x, source_excitation, g=None):
        # print(" Generator 1 : ", x.shape)

        # print(" Generator 2 : ", x.shape)
        if g is not None:
          x = x + self.cond(g)

        se = self.conv_process_se(source_excitation)
        se = F.leaky_relu(se, modules.LRELU_SLOPE)
        res_features = [se]
        for i in range(self.num_upsamples):
            in_size = se.size(2)
            se = self.downs[i](se)
            se = self.resblocks_downs[i](se)
            se = se[:, :, : in_size // self.upsample_rates[self.num_upsamples - 1 - i]]
            res_features.append(se)

        x = torch.cat([x, se], 1)
        x = self.conv_pre(x)

        for i in range(self.num_upsamples):
            x = F.leaky_relu(x, modules.LRELU_SLOPE)
            in_size = x.size(2)
            x = self.ups[i](x)
            # 保证维度正确，丢掉多余通道
            x = x[:, :, : in_size * self.upsample_rates[i]]

            # concat
            # gaussian_noise = torch.randn([x.shape[0], 8, x.shape[2]]).to(x.device)
            x = torch.cat([x, res_features[self.num_upsamples - 1 - i]], 1)
            x = self.concat_conv[i](x)

            xs = None
            for j in range(self.num_kernels):
                if xs is None:
                    x_ = self.AdaINs_before[i*self.num_kernels+j](x)
                    x_ = self.resblocks[i*self.num_kernels+j](x_)
                    xs = self.AdaINs_after[i*self.num_kernels+j](x_)
                else:
                    x_ = self.AdaINs_before[i*self.num_kernels+j](x)
                    x_ = self.resblocks[i*self.num_kernels+j](x_)
                    xs += self.AdaINs_after[i*self.num_kernels+j](x_)
            x = xs / self.num_kernels

        x = F.leaky_relu(x)
        x = self.conv_post(x)
        x = torch.tanh(x)

        return x

    def remove_weight_norm(self):
        print('Removing weight norm...')
        for l in self.ups:
            remove_weight_norm(l)
        for l in self.resblocks:
            l.remove_weight_norm()

class DiscriminatorLinear(torch.nn.Module):
    def __init__(self, kernel_size=5, stride=3, use_spectral_norm=False):
        super(DiscriminatorLinear, self).__init__()
        self.use_spectral_norm = use_spectral_norm
        norm_f = weight_norm if use_spectral_norm == False else spectral_norm
        padding = get_padding(kernel_size, stride)
        self.convs = nn.ModuleList([
            norm_f(Conv2d(1, 16, (kernel_size, kernel_size), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(16, 32, (kernel_size, kernel_size), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(32, 64, (kernel_size, kernel_size), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(64, 128, (kernel_size, kernel_size), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(128, 256, (kernel_size, kernel_size), 1, padding=(get_padding(kernel_size, 1), 0))),
        ])
        self.conv_post = norm_f(Conv2d(256, 1, (1, 1), 1, padding=(0, 0)))

    def forward(self, x):
        x = spectrogram_torch(x.squeeze(1),
                              2048,
                              44100,
                              256,
                              2048)
        # print("linear shape: ", x.shape)
        fmap = []

        # 1d to 2d
        b, c, t = x.shape
        # if t % self.period != 0: # pad first
        #     n_pad = self.period - (t % self.period)
        #     x = F.pad(x, (0, n_pad), "reflect")
        #     t = t + n_pad
        x = x.view(b, 1, c, t)

        for l in self.convs:
            x = l(x)
            x = F.leaky_relu(x, modules.LRELU_SLOPE)
            fmap.append(x)
        x = self.conv_post(x)
        fmap.append(x)
        x = torch.flatten(x, 1, -1)

        return x, fmap

class DiscriminatorP(torch.nn.Module):
    def __init__(self, period, kernel_size=5, stride=3, use_spectral_norm=False):
        super(DiscriminatorP, self).__init__()
        self.period = period
        self.use_spectral_norm = use_spectral_norm
        norm_f = weight_norm if use_spectral_norm == False else spectral_norm
        self.convs = nn.ModuleList([
            norm_f(Conv2d(1, 32, (kernel_size, 1), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(32, 128, (kernel_size, 1), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(128, 512, (kernel_size, 1), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(512, 1024, (kernel_size, 1), (stride, 1), padding=(get_padding(kernel_size, 1), 0))),
            norm_f(Conv2d(1024, 1024, (kernel_size, 1), 1, padding=(get_padding(kernel_size, 1), 0))),
        ])
        self.conv_post = norm_f(Conv2d(1024, 1, (3, 1), 1, padding=(1, 0)))

    def forward(self, x):
        fmap = []

        # 1d to 2d
        b, c, t = x.shape
        if t % self.period != 0: # pad first
            n_pad = self.period - (t % self.period)
            x = F.pad(x, (0, n_pad), "reflect")
            t = t + n_pad
        x = x.view(b, c, t // self.period, self.period)

        for l in self.convs:
            x = l(x)
            x = F.leaky_relu(x, modules.LRELU_SLOPE)
            fmap.append(x)
        x = self.conv_post(x)
        fmap.append(x)
        x = torch.flatten(x, 1, -1)

        return x, fmap


class DiscriminatorS(torch.nn.Module):
    def __init__(self, use_spectral_norm=False):
        super(DiscriminatorS, self).__init__()
        norm_f = weight_norm if use_spectral_norm == False else spectral_norm
        self.convs = nn.ModuleList([
            norm_f(Conv1d(1, 16, 15, 1, padding=7)),
            norm_f(Conv1d(16, 64, 41, 4, groups=4, padding=20)),
            norm_f(Conv1d(64, 256, 41, 4, groups=16, padding=20)),
            norm_f(Conv1d(256, 1024, 41, 4, groups=64, padding=20)),
            norm_f(Conv1d(1024, 1024, 41, 4, groups=256, padding=20)),
            norm_f(Conv1d(1024, 1024, 5, 1, padding=2)),
        ])
        self.conv_post = norm_f(Conv1d(1024, 1, 3, 1, padding=1))

    def forward(self, x):
        fmap = []

        for l in self.convs:
            x = l(x)
            x = F.leaky_relu(x, modules.LRELU_SLOPE)
            fmap.append(x)
        x = self.conv_post(x)
        fmap.append(x)
        x = torch.flatten(x, 1, -1)

        return x, fmap






class BaseFrequenceDiscriminator(nn.Module):
    def __init__(self, in_channels, hidden_channels=512):
        super(BaseFrequenceDiscriminator, self).__init__()

        self.discriminator = nn.ModuleList()
        self.discriminator += [
            nn.Sequential(
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    in_channels, hidden_channels // 32,
                    kernel_size=(3, 3), stride=(1, 1)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels // 32, hidden_channels // 16,
                    kernel_size=(3, 3), stride=(2, 2)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels // 16, hidden_channels // 8,
                    kernel_size=(3, 3), stride=(1, 1)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels // 8, hidden_channels // 4,
                    kernel_size=(3, 3), stride=(2, 2)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels // 4, hidden_channels // 2,
                    kernel_size=(3, 3), stride=(1, 1)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels // 2, hidden_channels,
                    kernel_size=(3, 3), stride=(2, 2)))
            ),
            nn.Sequential(
                nn.LeakyReLU(0.2, True),
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(nn.Conv2d(
                    hidden_channels, 1,
                    kernel_size=(3, 3), stride=(1, 1)))
            )
        ]

    def forward(self, x):
        hiddens = []
        for layer in self.discriminator:
            x = layer(x)
            hiddens.append(x)
        return x, hiddens[-1]

class FrameDiscriminator(nn.Module):
    def __init__(self, scale=1):
        super(FrameDiscriminator, self).__init__()


        self.discriminator = nn.ModuleList([
            nn.Sequential(
                nn.ReflectionPad1d(7),
                nn.utils.weight_norm(nn.Conv1d(1, 16 // scale, kernel_size=15)),
                nn.LeakyReLU(0.2, True),
            ),
            nn.Sequential(
                nn.utils.weight_norm(nn.Conv1d(16 // scale, 64 // scale, kernel_size=41,
                                     stride=4, padding=20, groups=4 // scale)),
                nn.LeakyReLU(0.2, True),
            ),
            nn.Sequential(
                nn.utils.weight_norm(nn.Conv1d(64 // scale, 256 // scale, kernel_size=41,
                                     stride=4, padding=20, groups=16 // scale)),
                nn.LeakyReLU(0.2, True),
            ),
            nn.Sequential(
                nn.utils.weight_norm(nn.Conv1d(256 // scale, 512 // scale, kernel_size=41,
                                     stride=4, padding=20, groups=64 // scale)),
                nn.LeakyReLU(0.2, True),
            ),
            nn.Sequential(
                nn.utils.weight_norm(nn.Conv1d(512 // scale, 512 // scale, kernel_size=5,
                                     stride=1, padding=2)),
                nn.LeakyReLU(0.2, True),
            ),
            nn.utils.weight_norm(nn.Conv1d(512 // scale, 1, kernel_size=3,
                                 stride=1, padding=1)),
        ])


    def forward(self, x):
        scores = list()
        for layer in self.discriminator:
            x = layer(x)
            scores.append(x)

        return scores
class MultiScaleFrameDiscriminator(nn.Module):
    def __init__(self):
        super(MultiScaleFrameDiscriminator, self).__init__()

        self.discriminators = nn.ModuleList(
            [FrameDiscriminator() for _ in range(3)]
        )

        self.downsample = nn.AvgPool1d(4, stride=2, padding=1, count_include_pad=False)

    def forward(self, y):
        scores = list()
        features = list()
        for layer in self.discriminators:
            score = layer(y)
            features.extend(score[:-1])
            scores.append(score[-1])

            y = self.downsample(y)
        return scores, features

class MultiFrequencyDiscriminator(nn.Module):
    def __init__(self):
        super(MultiFrequencyDiscriminator, self).__init__()
        self.discriminators = nn.ModuleDict()

        self.discriminators["multiscale"] = MultiScaleFrameDiscriminator()
        self.discriminators["frequency"] = FrequencyDiscriminatorV4()

    def forward(self, y):
        scores, feats = dict(), dict()
        for key, dis in self.discriminators.items():
            s, f = dis(y)
            scores[key] = s
            feats[key] = f
        return scores, feats

class MultiPeriodDiscriminator(torch.nn.Module):
    def __init__(self, use_spectral_norm=False):
        super(MultiPeriodDiscriminator, self).__init__()
        # periods = [2,3,5,7,11]

        discs = [DiscriminatorS(use_spectral_norm=use_spectral_norm)]
        # discs = discs + [DiscriminatorP(i, use_spectral_norm=use_spectral_norm) for i in periods]
        #discs = discs + [DiscriminatorLinear(kernel_size=3, stride=3)]
        self.discriminators = nn.ModuleList(discs)
        self.disc_multspec = MultiFrequencyDiscriminator()
        # self.disc_multspec_uv = MultiFrequencyDiscriminator(hop_lengths=[128, 256],hidden_channels=[256, 512])

    def forward(self, y, y_hat):
        y_d_rs = []
        y_d_gs = []
        fmap_rs = []
        fmap_gs = []
        for i, d in enumerate(self.discriminators):
            y_d_r, fmap_r = d(y)
            y_d_g, fmap_g = d(y_hat)
            y_d_rs.append(y_d_r)
            y_d_gs.append(y_d_g)
            fmap_rs.append(fmap_r)
            fmap_gs.append(fmap_g)
        scores_r, fmaps_r = self.disc_multspec(y)
        scores_g, fmaps_g = self.disc_multspec(y_hat)
        for i in range(len(scores_r)):
            y_d_rs.append(scores_r[i])
            y_d_gs.append(scores_g[i])
            fmap_rs.append(fmaps_r[i])
            fmap_gs.append(fmaps_g[i])

        # scores_r, fmaps_r = self.disc_multspec_uv(y)
        # scores_g, fmaps_g = self.disc_multspec_uv(y_hat)

        # for i in range(len(scores_r)):
        #     y_d_rs.append(scores_r[i])
        #     y_d_gs.append(scores_g[i])
        #     fmap_rs.append(fmaps_r[i])
        #     fmap_gs.append(fmaps_g[i])

        return y_d_rs, y_d_gs, fmap_rs, fmap_gs


# def SourceExcitation(F0, scale_factor=300, source_excitation=16):
#     Source = torch.nn.functional.interpolate(F0, scale_factor=scale_factor)
#     Source = Source * 2 * np.pi / 24000
#     # Source = torch.tile(Source, [1, 32, 1])
#     Source = Source.repeat(1, source_excitation, 1)
#     harmonic_index = torch.range(0, source_excitation - 1)
#     harmonic_index = torch.unsqueeze(harmonic_index, 0)
#     harmonic_index = torch.unsqueeze(harmonic_index, 2).to(F0.device)
#     Source = Source * harmonic_index
#     print("shape: ", Source.shape)
#     sum_matrix = torch.ones([Source.size(2), Source.size(2)]).to(F0.device)
#     sum_matrix = torch.triu(sum_matrix)
#     Source = torch.matmul(Source, sum_matrix)
#     Source = torch.sin(Source) * 0.1 + 0.001 * torch.randn(Source.shape).to(F0.deivce)

#     return Source

def SourceExcitation(F0, scale_factor=300, source_excitation=32):
    F0 = F0.data.cpu().float().numpy().reshape(-1)
    x = np.arange(0, F0.shape[0])
    x_new = np.arange(0, F0.shape[0] * scale_factor)
    tck = interpolate.splrep(x, F0)
    Source = interpolate.splev(x_new, tck)

    Source = Source / 24000
    Source = np.expand_dims(Source, 1)
    Source = Source.repeat( source_excitation, axis=1)

    harmonic_index = np.arange(1, source_excitation + 1)
    harmonic_index = np.expand_dims(harmonic_index, 0)
    Source = Source * harmonic_index

    for i in range(1, len(Source)):
        Source[i] = Source[i] + Source[i-1]
        Source[i] = Source[i] - Source[i] // 1
        Source[i] = Source[i] * 2 * np.pi
    Source = np.array(Source)
    Source = np.sin(Source) * 0.1 + 0.001 * np.random.randn(Source.shape[0], Source.shape[1])
    Source = Source.T.reshape([1, source_excitation, -1])
    Source = torch.from_numpy(Source).float().cuda(0)
    return Source


def count_parameters(model):
    return sum(p.numel() for p in model.parameters() if p.requires_grad)




class SynthesizerTrn_3k(nn.Module):
  """
  Synthesizer for Training
  """

  def __init__(self,
    n_vocab,
    spec_channels,
    segment_size,
    inter_channels,
    hidden_channels,
    filter_channels,
    n_heads,
    n_layers,
    kernel_size,
    p_dropout,
    resblock,
    resblock_kernel_sizes,
    resblock_dilation_sizes,
    upsample_rates,
    upsample_initial_channel,
    upsample_kernel_sizes,
    n_speakers=0,
    gin_channels=0,
    use_sdp=False,
    **kwargs):

    super().__init__()
    self.n_vocab = n_vocab
    self.spec_channels = spec_channels
    self.inter_channels = inter_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout
    self.resblock = resblock
    self.resblock_kernel_sizes = resblock_kernel_sizes
    self.resblock_dilation_sizes = resblock_dilation_sizes
    self.upsample_rates = upsample_rates
    self.upsample_initial_channel = upsample_initial_channel
    self.upsample_kernel_sizes = upsample_kernel_sizes
    self.segment_size = segment_size
    self.n_speakers = n_speakers
    self.gin_channels = gin_channels

    # self.dec = Generator(80, resblock, resblock_kernel_sizes, resblock_dilation_sizes, upsample_rates, upsample_initial_channel, upsample_kernel_sizes, gin_channels=gin_channels)
    self.hop_length = 200
    self.win_size = 2048
    #self.max_pool = nn.MaxPool1d(self.win_size, self.hop_length)

    self.nhv = NeuralHomomorphicVocoder(postfilter_type='conv')

    # print("Generator params : ", count_parameters(self.dec) / 1000_000)
    print("nhv params : ", count_parameters(self.nhv) / 1000_000)

  def forward(self, mel_spec, mel_lengths, LF0, uv):
    # HiFiGAN vocoder
    z_slice, ids_slice = commons.rand_slice_segments(mel_spec, mel_lengths, self.segment_size)
    # source_excitation = commons.slice_segments(se, ids_slice * self.hop_length, self.segment_size * self.hop_length)
    F0 = commons.slice_segments(LF0, ids_slice, self.segment_size)
    uv = commons.slice_segments(uv, ids_slice, self.segment_size)
    #F0 = F0 * 50 + 300
    #F0 = F0 / 2595.
    #F0 = torch.pow(10, F0)
    #F0 = F0 - 1
    #F0 = F0 * 700
    prior_audio_6k, origin, uv_upsample = self.nhv(torch.zeros([z_slice.shape[0], 1, z_slice.shape[2] * self.hop_length]).to(z_slice.device), torch.cat([z_slice, F0, uv], 1))

    # print("forward shape : ", z_slice.shape, prior_audio.shape)
    # o = self.dec(z_slice, prior_audio.detach(), g=None)
    #o = torch.zeros_like(prior_audio)
    return prior_audio_6k, origin, uv_upsample, z_slice, ids_slice

  def infer(self, mel_spec, LF0, uv, F0=None, sid=None, noise_scale=1, length_scale=1, noise_scale_w=1., max_len=None):

    # source_excitation = SourceExcitation(F0, 300)
    # source_excitation = se
    # if(source_excitation.shape[2] > mel_spec.shape[2] * self.hop_length):
    #     source_excitation = source_excitation[:,:,:mel_spec.shape[2] * self.hop_length]
    # elif(source_excitation.shape[2] < mel_spec.shape[2] * self.hop_length):
    #     source_excitation = torch.cat([source_excitation, torch.zeros([1, 32, mel_spec.shape[1] * self.hop_length - source_excitation.shape[0]])], -1)
    F0 = LF0
    #F0 = F0 * 50 + 300
    #F0 = F0 / 2595.
    #F0 = torch.pow(10, F0)
    #F0 = F0 - 1
    #F0 = F0 * 700
    prior_audio_6k, origin, uv_upsample = self.nhv(torch.zeros([mel_spec.shape[0], 1, mel_spec.shape[2] * self.hop_length]).to(mel_spec.device), torch.cat([mel_spec, F0, uv], 1))

    # print('infer : ', mel_spec.shape, prior_audio.shape, mel_spec.device, prior_audio.device )
    # HiFUGAN vocoder
    # o = self.dec(mel_spec[:,:,:max_len], prior_audio, g=None)

    return prior_audio_6k, origin, uv_upsample



class SynthesizerTrn(nn.Module):
  """
  Synthesizer for Training
  """

  def __init__(self,
    n_vocab,
    spec_channels,
    segment_size,
    inter_channels,
    hidden_channels,
    filter_channels,
    n_heads,
    n_layers,
    kernel_size,
    p_dropout,
    resblock,
    resblock_kernel_sizes,
    resblock_dilation_sizes,
    upsample_rates,
    upsample_initial_channel,
    upsample_kernel_sizes,
    n_speakers=0,
    gin_channels=0,
    use_sdp=False,
    **kwargs):

    super().__init__()
    self.n_vocab = n_vocab
    self.spec_channels = spec_channels
    self.inter_channels = inter_channels
    self.hidden_channels = hidden_channels
    self.filter_channels = filter_channels
    self.n_heads = n_heads
    self.n_layers = n_layers
    self.kernel_size = kernel_size
    self.p_dropout = p_dropout
    self.resblock = resblock
    self.resblock_kernel_sizes = resblock_kernel_sizes
    self.resblock_dilation_sizes = resblock_dilation_sizes
    self.upsample_rates = upsample_rates
    self.upsample_initial_channel = upsample_initial_channel
    self.upsample_kernel_sizes = upsample_kernel_sizes
    self.segment_size = segment_size
    self.n_speakers = n_speakers
    self.gin_channels = gin_channels

    # self.dec = Generator(80, resblock, resblock_kernel_sizes, resblock_dilation_sizes, upsample_rates, upsample_initial_channel, upsample_kernel_sizes, gin_channels=gin_channels)
    self.hop_length = 256
    self.win_size = 2048
    #self.max_pool = nn.MaxPool1d(self.win_size, self.hop_length)



    # print("Generator params : ", count_parameters(self.dec) / 1000_000)
    self.melgan = Melgan()

  def forward(self, audio_6k, origin, mel, uv):

    prior_audio = self.melgan(audio_6k, origin, mel, uv)

    # print("forward shape : ", z_slice.shape, prior_audio.shape)
    # o = self.dec(z_slice, prior_audio.detach(), g=None)
    #o = torch.zeros_like(prior_audio)
    return prior_audio

  def infer(self, audio_6k, origin, mel, uv):
    prior_audio = self.melgan.infer(audio_6k, origin, mel, uv)
    return prior_audio
