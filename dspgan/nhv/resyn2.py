import sys
import os
import json
import math
import torch
import time
from torch import nn
from torch.nn import functional as F
from torch.utils.data import DataLoader

import dspgan.nhv.commons
from dspgan.nhv.utils import get_hparams_from_file
from dspgan.nhv.utils import load_checkpoint
from dspgan.nhv.models_newamp_exp16_4_hifi_band_48 import SynthesizerTrn
from dspgan.nhv.models_newamp_exp16_4_hifi_band_48 import SynthesizerTrn_3k

from scipy.io.wavfile import write
import numpy as np

from scipy.interpolate import interp1d
from dspgan.nhv.mel_processing import mel_spectrogram_torch_nhv2

net_g = None
net_g_3k = None
hps = None


def interp1d_new(pitch):
    pitch = pitch.reshape(-1)
    nonzero_ids = np.where(pitch > 0)[0]
    if len(nonzero_ids) > 1:
        interp_fn = interp1d(
            nonzero_ids,
            pitch[nonzero_ids],
            fill_value=(pitch[nonzero_ids[0]], pitch[nonzero_ids[-1]]),
            bounds_error=False,
        )
        pitch = interp_fn(np.arange(0, len(pitch)))
    return pitch.reshape(-1, 1)

def generate_audio(mel_path, pitch_path, model_name='model', use_cuda=False):
    global net_g_3k, net_g, hps
    device = torch.device('cuda' if use_cuda and torch.cuda.is_available() else 'cpu')
    if net_g_3k is None or net_g is None or hps is None:
        hps = get_hparams_from_file("dspgan/nhv/configs/bigdata_16to48k.json")
        net_g_3k = SynthesizerTrn_3k(
            250,
            hps.data.filter_length // 2 + 1,
            hps.train.segment_size // hps.data.hop_length,
            **hps.model)
        net_g = SynthesizerTrn(
            250,
            hps.data.filter_length // 2 + 1,
            hps.train.segment_size // hps.data.hop_length,
            **hps.model)

        net_g.to(device)
        net_g_3k.to(device)

        _ = net_g_3k.eval()
        _ = net_g.eval()

        _ = load_checkpoint("dspgan/nhv/ckpts/G3k_600000.pth", net_g_3k, None)
        _ = load_checkpoint(f"dspgan/nhv/ckpts/{model_name}.pth", net_g, None)

    # mel = np.load(mel_path, allow_pickle=True)
    mel = mel_path
    if mel.shape[0] != 80:
        mel = mel.transpose(1, 0)
    # LF0 = np.load(pitch_path)
    LF0 = pitch_path
    uv = np.zeros(LF0.shape, dtype=np.float32)
    uv[LF0 > 0] = 1
    LF0 = np.where(LF0>0., np.exp(LF0), 0.)
    LF0 = interp1d_new(LF0)
    LF0 = LF0.reshape([1, -1])
    uv = uv.reshape([1, -1])

    with torch.no_grad():
        #print(file_name)
        mel = torch.from_numpy(mel).float().to(device)
        LF0 = torch.from_numpy(LF0).float().to(device)
        uv = torch.from_numpy(uv).float().to(device)
        mel = torch.unsqueeze(mel, 0)
        LF0 = torch.unsqueeze(LF0, 0)
        uv = torch.unsqueeze(uv, 0)
        if(LF0.shape[2] > mel.shape[2] and LF0.shape[2] - mel.shape[2] < 10):
            LF0 = LF0[:, :, :mel.shape[2]]
        if(LF0.shape[2] < mel.shape[2]):
            mel = mel[:, :, :LF0.shape[2]]
        start_time = time.time()
        prior_audio_6k, harmonic_noise, uv_upsample = net_g_3k.infer(mel, LF0, uv)
        nhv_mel,res_mel = mel_spectrogram_torch_nhv2(mel, prior_audio_6k.squeeze(1), 1024, hps.data.n_mel_channels, 16000, 200, 800, 0, 8000, hps.data.min_db, hps.data.max_abs_value, hps.data.min_level_db, hps.data.ref_level_db)
        # audio_6k = prior_audio_6k.squeeze().data.cpu().float().numpy()
        sin = uv_upsample.squeeze().data.cpu().float().numpy()
        prior_audio = net_g(nhv_mel, res_mel, mel, uv_upsample)
        end_time = time.time()
        audio = prior_audio.squeeze().data.cpu().float().numpy()

    audio = audio * hps.data.max_wav_value
    audio = audio.astype(np.int16)
    # audio_6k = audio_6k * hps.data.max_wav_value
    # audio_6k = audio_6k.astype(np.int16)
    return audio
    # write(out_path, 48000, audio)
    

