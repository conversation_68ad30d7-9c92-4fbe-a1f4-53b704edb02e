import time
import os
import random
import numpy as np
import torch
import torch.utils.data
from scipy.interpolate import interp1d

import commons
from mel_processing import spectrogram_torch, mel_spectrogram_torch, spec_to_mel_torch
from utils import load_wav_to_torch, load_filepaths_and_text
# from text import text_to_sequence, cleaned_text_to_sequence, cleaned_text_to_sequence_ASLPTTSing, cleaned_text_to_sequence_ASLP, tones_to_sequence_ASLP, seg_tag_to_sequence_ASLP, prosody_to_sequence_ASLP


class TextAudioLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 190)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        for audiopath, text in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text
        audiopath, text = audiopath_and_text[0], audiopath_and_text[1]
        text = self.get_text(text)
        spec, wav = self.get_audio(audiopath)
        return (text, spec, wav)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, text):
        if self.cleaned_text:
            text_norm = cleaned_text_to_sequence(text)
        else:
            text_norm = text_to_sequence(text, self.text_cleaners)
        if self.add_blank:
            text_norm = commons.intersperse(text_norm, 0)
        text_norm = torch.LongTensor(text_norm)
        return text_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class TextAudioCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_text_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])

        text_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))

        text_padded = torch.LongTensor(len(batch), max_text_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        text_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            text = row[0]
            text_padded[i, :text.size(0)] = text
            text_lengths[i] = text.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

        if self.return_ids:
            return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, ids_sorted_decreasing
        return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths


"""Multi speaker version"""
class TextAudioSpeakerLoader(torch.utils.data.Dataset):
    """
        1) loads audio, speaker_id, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_sid_text, hparams):
        self.audiopaths_sid_text = load_filepaths_and_text(audiopaths_sid_text)
        self.text_cleaners = hparams.text_cleaners
        self.max_wav_value = hparams.max_wav_value
        self.sampling_rate = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 190)

        random.seed(1234)
        random.shuffle(self.audiopaths_sid_text)
        self._filter()

    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_sid_text_new = []
        lengths = []
        for audiopath, sid, text in self.audiopaths_sid_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_sid_text_new.append([audiopath, sid, text])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_sid_text = audiopaths_sid_text_new
        self.lengths = lengths

    def get_audio_text_speaker_pair(self, audiopath_sid_text):
        # separate filename, speaker_id and text
        audiopath, sid, text = audiopath_sid_text[0], audiopath_sid_text[1], audiopath_sid_text[2]
        text = self.get_text(text)
        spec, wav = self.get_audio(audiopath)
        sid = self.get_sid(sid)
        return (text, spec, wav, sid)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, text):
        if self.cleaned_text:
            text_norm = cleaned_text_to_sequence(text)
        else:
            text_norm = text_to_sequence(text, self.text_cleaners)
        if self.add_blank:
            text_norm = commons.intersperse(text_norm, 0)
        text_norm = torch.LongTensor(text_norm)
        return text_norm

    def get_sid(self, sid):
        sid = torch.LongTensor([int(sid)])
        return sid

    def __getitem__(self, index):
        return self.get_audio_text_speaker_pair(self.audiopaths_sid_text[index])

    def __len__(self):
        return len(self.audiopaths_sid_text)


class TextAudioSpeakerCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text, audio and speaker identities
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized, sid]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_text_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])

        text_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        sid = torch.LongTensor(len(batch))

        text_padded = torch.LongTensor(len(batch), max_text_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        text_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            text = row[0]
            text_padded[i, :text.size(0)] = text
            text_lengths[i] = text.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            sid[i] = row[3]

        if self.return_ids:
            return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, sid, ids_sorted_decreasing
        return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, sid



class TextAudioLF0Loader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 190)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        for audiopath, text, LF0path in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text, LF0path])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text
        audiopath, text, LF0path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        text = self.get_text(text)
        spec, wav = self.get_audio(audiopath)
        LF0 = np.load(LF0path)

        if(abs(LF0.shape[0] - spec.shape[1]) > 5):
            raise
        if(LF0.shape[0] > spec.shape[1]):
            LF0 = LF0[:spec.shape[1]]
        elif(LF0.shape[0] < spec.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        assert LF0.shape[0] == spec.shape[1]
        LF0 = LF0.reshape([ 1, -1])
        LF0 = torch.from_numpy(LF0)
        return (text, spec, wav, LF0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, text):
        if self.cleaned_text:
            text_norm = cleaned_text_to_sequence(text)
        else:
            text_norm = text_to_sequence(text, self.text_cleaners)
        if self.add_blank:
            text_norm = commons.intersperse(text_norm, 0)
        text_norm = torch.LongTensor(text_norm)
        return text_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class TextAudioLF0Collate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_text_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])
        max_LF0_len = max([x[3].size(1) for x in batch])

        text_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))

        text_padded = torch.LongTensor(len(batch), max_text_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        text_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        LF0_padded.zero_()
        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            text = row[0]
            text_padded[i, :text.size(0)] = text
            text_lengths[i] = text.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            LF0 = row[3]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)
        # print("LF0 padded shape : ", LF0_padded.shape)
        if self.return_ids:
            return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, LF0_padded, LF0_lengths, ids_sorted_decreasing
        return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, LF0_padded, LF0_lengths


class ASLPTextAudioLF0Loader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 200)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()
        print(" DATA SET len : ", len(self.audiopaths_and_text))


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        for audiopath, text, LF0path, dur in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text, LF0path, dur])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text
        audiopath, text, LF0path, dur = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        text = self.get_text(text)
        spec, wav = self.get_audio(audiopath)

        LF0 = np.load(LF0path)
        if(abs(LF0.shape[0] - spec.shape[1]) > 5):
            raise
        if(LF0.shape[0] > spec.shape[1]):
            LF0 = LF0[:spec.shape[1]]
        elif(LF0.shape[0] < spec.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        dur = dur.split()
        dur = [int(d) for d in dur]
        dur = np.array(dur)
        sum_dur = np.sum(dur)
        if(abs(sum_dur - spec.shape[1]) >= 5):
            raise
        if(sum_dur > spec.shape[1]):
            dur[-1] = dur[-1] - (sum_dur - spec.shape[1])
        elif(sum_dur < spec.shape[1]):
            dur[-1] = dur[-1] + ( spec.shape[1] - sum_dur)

        assert LF0.shape[0] == spec.shape[1]
        assert dur.shape[0] == text.size(0), print(dur, text)

        dur = dur.reshape([ 1, -1])
        dur = torch.from_numpy(dur)
        LF0 = LF0.reshape([ 1, -1])
        LF0 = torch.from_numpy(LF0)
        return (text, spec, wav, LF0, dur)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, text):
        if self.cleaned_text:
            text_norm = cleaned_text_to_sequence_ASLP(text)
        else:
            text_norm = text_to_sequence(text, self.text_cleaners)

        # if self.add_blank:
        #     text_norm = commons.intersperse(text_norm, 0)
        text_norm = torch.LongTensor(text_norm)
        return text_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPTextAudioLF0Collate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_text_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])
        max_LF0_len = max([x[3].size(1) for x in batch])
        max_dur_len = max([x[4].size(1) for x in batch])

        text_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))
        dur_lengths = torch.LongTensor(len(batch))

        text_padded = torch.LongTensor(len(batch), max_text_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        dur_padded = torch.LongTensor(len(batch), 1, max_dur_len)

        text_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        LF0_padded.zero_()
        dur_padded.zero_()
        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            text = row[0]
            text_padded[i, :text.size(0)] = text
            text_lengths[i] = text.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            LF0 = row[3]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)

            dur = row[4]
            dur_padded[i, :, :dur.size(1)] = dur
            dur_lengths[i] = dur.size(1)
        if self.return_ids:
            return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths, ids_sorted_decreasing
        return text_padded, text_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths


class ASLPLabelAudioLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 190)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for audiopath, text, tones, seg_tags, prosodys, LF0path, dur, bnpath in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text, tones, seg_tags, prosodys, LF0path, dur, bnpath])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        audiopath, phones, tones, seg_tags, prosodys, LF0path, dur, bnpath = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3], audiopath_and_text[4], audiopath_and_text[5], audiopath_and_text[6], audiopath_and_text[7]
        phones, tones, seg_tags, prosodys = self.get_text(phones, tones, seg_tags, prosodys)
        spec, wav = self.get_audio(audiopath)
        # print(' info : ', audiopath, phones, spec.shape, wav.shape)

        dur = dur.split()
        dur = [int(d) for d in dur]
        dur = np.array(dur)
        sum_dur = np.sum(dur)
        if(abs(sum_dur - spec.shape[1]) >= 5):
            print(sum_dur , spec.shape[1])
            raise "dur len error ! "
        if(sum_dur > spec.shape[1]):
            dur[-1] = dur[-1] - (sum_dur - spec.shape[1])
        elif(sum_dur < spec.shape[1]):
            dur[-1] = dur[-1] + ( spec.shape[1] - sum_dur)


        LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]], dtype=np.float32)
        if(abs(LF0.shape[0] - spec.shape[1]) > 10):
            raise
        if(LF0.shape[0] > spec.shape[1]):
            LF0 = LF0[:spec.shape[1]]
        elif(LF0.shape[0] < spec.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        # bn = np.load(bnpath)
        bn = np.zeros([spec.shape[1], 256], dtype=np.float32)
        if(abs(bn.shape[0] - spec.shape[1]) > 10):
            print(bn.shape, spec.shape)
            raise
        if(bn.shape[0] > spec.shape[1]):
            bn = bn[:spec.shape[1]]
        elif(bn.shape[0] < spec.shape[1]):
            bn = np.concatenate([bn, np.zeros([spec.shape[1] - bn.shape[0], 256])], axis=0)

        assert LF0.shape[0] == spec.shape[1]
        assert bn.shape[0] == spec.shape[1]
        assert dur.shape[0] == phones.size(0), print(dur, phones)

        dur = dur.reshape([ 1, -1])
        dur = torch.from_numpy(dur)

        LF0 = LF0.reshape([ 1, -1])
        LF0 = torch.from_numpy(LF0)

        bn = bn.T
        bn = torch.from_numpy(bn)
        return (phones, spec, wav, tones, seg_tags, prosodys, LF0, dur, bn)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, phones, tones, seg_tags, prosodys):
        phones = cleaned_text_to_sequence_ASLP(phones)
        tones = tones_to_sequence_ASLP(tones)
        seg_tags = seg_tag_to_sequence_ASLP(seg_tags)
        prosodys = prosody_to_sequence_ASLP(prosodys)

        phones = torch.LongTensor(phones)
        tones = torch.LongTensor(tones)
        seg_tags = torch.LongTensor(seg_tags)
        prosodys = torch.LongTensor(prosodys)
        return phones, tones, seg_tags, prosodys

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)



class ASLPLabelAudioCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_phone_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])
        max_tone_len = max([len(x[3]) for x in batch])
        max_seg_tag_len = max([len(x[4]) for x in batch])
        max_prosody_len = max([len(x[5]) for x in batch])
        max_LF0_len = max([x[6].size(1) for x in batch])
        max_dur_len = max([x[7].size(1) for x in batch])
        max_bn_len = max([x[8].size(1) for x in batch])

        phone_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        tone_lengths = torch.LongTensor(len(batch))
        seg_tag_lengths = torch.LongTensor(len(batch))
        prosody_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))
        dur_lengths = torch.LongTensor(len(batch))
        bn_lengths = torch.LongTensor(len(batch))

        phone_padded = torch.LongTensor(len(batch), max_phone_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        tone_padded = torch.LongTensor(len(batch), max_tone_len)
        seg_tag_padded = torch.LongTensor(len(batch), max_seg_tag_len)
        prosody_padded = torch.LongTensor(len(batch), max_prosody_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        dur_padded = torch.LongTensor(len(batch), 1, max_dur_len)
        bn_padded = torch.FloatTensor(len(batch), 256, max_bn_len)

        phone_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        tone_padded.zero_()
        seg_tag_padded.zero_()
        prosody_padded.zero_()
        LF0_padded.zero_()
        dur_padded.zero_()
        bn_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            phone = row[0]
            phone_padded[i, :phone.size(0)] = phone
            phone_lengths[i] = phone.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            tone = row[3]
            tone_padded[i, :tone.size(0)] = tone
            tone_lengths[i] = tone.size(0)

            seg_tag = row[4]
            seg_tag_padded[i, :seg_tag.size(0)] = seg_tag
            seg_tag_lengths[i] = seg_tag.size(0)

            prosody = row[5]
            prosody_padded[i, :prosody.size(0)] = prosody
            prosody_lengths[i] = prosody.size(0)

            LF0 = row[6]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)

            dur = row[7]

            dur_padded[i, :, :dur.size(1)] = dur
            dur_lengths[i] = dur.size(1)


            bn = row[8]
            bn_padded[i, :, :bn.size(1)] = bn
            bn_lengths[i] = bn.size(1)

        if self.return_ids:
            return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, tone_padded, tone_lengths, seg_tag_padded, seg_tag_lengths, prosody_padded, prosody_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths, bn_padded, bn_lengths, ids_sorted_decreasing
        return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, tone_padded, tone_lengths, seg_tag_padded, seg_tag_lengths, prosody_padded, prosody_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths, bn_padded, bn_lengths


class MultSpeakerLabelAudioLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 190)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for audiopath, text, tones, seg_tags, prosodys, LF0path, dur, bnpath, spkid in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text, tones, seg_tags, prosodys, LF0path, dur, bnpath, spkid])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        audiopath, phones, tones, seg_tags, prosodys, LF0path, dur, bnpath, spkid = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3], audiopath_and_text[4], audiopath_and_text[5], audiopath_and_text[6], audiopath_and_text[7], audiopath_and_text[8]
        phones, tones, seg_tags, prosodys = self.get_text(phones, tones, seg_tags, prosodys)
        spec, wav = self.get_audio(audiopath)
        # print(' info : ', audiopath, phones, spec.shape, wav.shape)

        dur = dur.split()
        dur = [int(d) for d in dur]
        dur = np.array(dur)
        sum_dur = np.sum(dur)
        if(abs(sum_dur - spec.shape[1]) >= 5):
            print(sum_dur , spec.shape[1])
            raise "dur len error ! "
        if(sum_dur > spec.shape[1]):
            dur[-1] = dur[-1] - (sum_dur - spec.shape[1])
        elif(sum_dur < spec.shape[1]):
            dur[-1] = dur[-1] + ( spec.shape[1] - sum_dur)


        LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]], dtype=np.float32)
        if(abs(LF0.shape[0] - spec.shape[1]) > 10):
            raise
        if(LF0.shape[0] > spec.shape[1]):
            LF0 = LF0[:spec.shape[1]]
        elif(LF0.shape[0] < spec.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        #bn = np.load(bnpath)
        bn = np.zeros([spec.shape[1], 256])
        if(abs(bn.shape[0] - spec.shape[1]) > 10):
            print(bn.shape, spec.shape)
            raise
        if(bn.shape[0] > spec.shape[1]):
            bn = bn[:spec.shape[1]]
        elif(bn.shape[0] < spec.shape[1]):
            bn = np.concatenate([bn, np.zeros([spec.shape[1] - bn.shape[0], 256])], axis=0)

        assert LF0.shape[0] == spec.shape[1]
        assert bn.shape[0] == spec.shape[1]
        assert dur.shape[0] == phones.size(0), print(dur, phones)

        dur = dur.reshape([ 1, -1])
        dur = torch.from_numpy(dur)

        LF0 = LF0.reshape([ 1, -1])
        LF0 = torch.from_numpy(LF0)

        bn = bn.T
        bn = torch.from_numpy(bn)

        spkid = torch.LongTensor([int(spkid)]).reshape([1])
        return (phones, spec, wav, tones, seg_tags, prosodys, LF0, dur, bn, spkid)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, phones, tones, seg_tags, prosodys):
        phones = cleaned_text_to_sequence_ASLP(phones)
        tones = tones_to_sequence_ASLP(tones)
        seg_tags = seg_tag_to_sequence_ASLP(seg_tags)
        prosodys = prosody_to_sequence_ASLP(prosodys)

        phones = torch.LongTensor(phones)
        tones = torch.LongTensor(tones)
        seg_tags = torch.LongTensor(seg_tags)
        prosodys = torch.LongTensor(prosodys)
        return phones, tones, seg_tags, prosodys

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class MultSpeakerLabelAudioCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_phone_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])
        max_tone_len = max([len(x[3]) for x in batch])
        max_seg_tag_len = max([len(x[4]) for x in batch])
        max_prosody_len = max([len(x[5]) for x in batch])
        max_LF0_len = max([x[6].size(1) for x in batch])
        max_dur_len = max([x[7].size(1) for x in batch])
        max_bn_len = max([x[8].size(1) for x in batch])

        phone_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        tone_lengths = torch.LongTensor(len(batch))
        seg_tag_lengths = torch.LongTensor(len(batch))
        prosody_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))
        dur_lengths = torch.LongTensor(len(batch))
        bn_lengths = torch.LongTensor(len(batch))

        phone_padded = torch.LongTensor(len(batch), max_phone_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        tone_padded = torch.LongTensor(len(batch), max_tone_len)
        seg_tag_padded = torch.LongTensor(len(batch), max_seg_tag_len)
        prosody_padded = torch.LongTensor(len(batch), max_prosody_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        dur_padded = torch.LongTensor(len(batch), 1, max_dur_len)
        bn_padded = torch.FloatTensor(len(batch), 256, max_bn_len)
        spkid_padded = torch.LongTensor(len(batch))

        phone_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        tone_padded.zero_()
        seg_tag_padded.zero_()
        prosody_padded.zero_()
        LF0_padded.zero_()
        dur_padded.zero_()
        bn_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            phone = row[0]
            phone_padded[i, :phone.size(0)] = phone
            phone_lengths[i] = phone.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            tone = row[3]
            tone_padded[i, :tone.size(0)] = tone
            tone_lengths[i] = tone.size(0)

            seg_tag = row[4]
            seg_tag_padded[i, :seg_tag.size(0)] = seg_tag
            seg_tag_lengths[i] = seg_tag.size(0)

            prosody = row[5]
            prosody_padded[i, :prosody.size(0)] = prosody
            prosody_lengths[i] = prosody.size(0)

            LF0 = row[6]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)

            dur = row[7]

            dur_padded[i, :, :dur.size(1)] = dur
            dur_lengths[i] = dur.size(1)

            bn = row[8]
            bn_padded[i, :, :bn.size(1)] = bn
            bn_lengths[i] = bn.size(1)

            spkid = row[9]
            spkid_padded[i] = spkid

        if self.return_ids:
            return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, tone_padded, tone_lengths, seg_tag_padded, seg_tag_lengths, prosody_padded, prosody_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths, bn_padded, bn_lengths, spkid_padded, ids_sorted_decreasing
        return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, tone_padded, tone_lengths, seg_tag_padded, seg_tag_lengths, prosody_padded, prosody_lengths, LF0_padded, LF0_lengths, dur_padded, dur_lengths, bn_padded, bn_lengths, spkid_padded


class ASLPTTSingLabelAudioLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_and_text_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for audiopath, text, pitchid, slur, pos, dur, LF0path, dur_target, numpho in self.audiopaths_and_text:
            if self.min_text_len <= len(text) and len(text) <= self.max_text_len:
                audiopaths_and_text_new.append([audiopath, text, pitchid, slur, pos, dur, LF0path, dur_target, numpho])
                lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_and_text_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        audiopath, phones, pitchid, slur, pos, dur, LF0path, dur_target, numpho = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3], audiopath_and_text[4], audiopath_and_text[5], audiopath_and_text[6], audiopath_and_text[7], audiopath_and_text[8]
        phones, pitchid, slur, pos, dur, numpho = self.get_text(phones, pitchid, slur, pos, dur, numpho)
        spec, wav = self.get_audio(audiopath)
        # print(' info : ', audiopath, phones, spec.shape, wav.shape)

        dur_target = dur_target.split()
        dur_target = [int(d) for d in dur_target]
        dur_target = np.array(dur_target)
        sum_dur = np.sum(dur_target)
        if(abs(sum_dur - spec.shape[1]) >= 5):
            # print(sum_dur , spec.shape[1])
            return None
            # raise "dur len error ! "
        if(sum_dur > spec.shape[1]):
            dur_target[-1] = dur_target[-1] - (sum_dur - spec.shape[1])
        elif(sum_dur < spec.shape[1]):
            dur_target[-1] = dur_target[-1] + ( spec.shape[1] - sum_dur)


        LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]], dtype=np.float32)

        if(abs(LF0.shape[0] - spec.shape[1]) > 5):
            print("LF0 shape: ", LF0.shape, spec.shape)
            return None
            # raise
        if(LF0.shape[0] > spec.shape[1]):
            LF0 = LF0[:spec.shape[1]]
        elif(LF0.shape[0] < spec.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        assert LF0.shape[0] == spec.shape[1]
        assert dur_target.shape[0] == phones.size(0), print(LF0path, dur_target.shape, phones.shape)

        dur_target = dur_target.reshape([ 1, -1])
        dur_target = torch.from_numpy(dur_target)

        LF0 = LF0.reshape([ 1, -1])
        LF0 = torch.from_numpy(LF0)
        return (phones, spec, wav, pitchid, slur, pos, dur, LF0, dur_target, numpho)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def get_text(self, phones, pitchid, slur, pos, dur, numpho):
        phones = cleaned_text_to_sequence_ASLPTTSing(phones)
        pitchid = [int(p) for p in pitchid.split()]
        slur = [int(s) for s in slur.split()]
        pos = [int(p) for p in pos.split()]
        dur = [float(d) for d in dur.split()]
        numpho = [int(n) for n in numpho.split()]

        phones = torch.LongTensor(phones)
        pitchid = torch.LongTensor(pitchid)
        slur = torch.LongTensor(slur)
        pos = torch.LongTensor(pos)
        dur = torch.FloatTensor(dur)
        numpho = torch.FloatTensor(numpho)
        return phones, pitchid, slur, pos, dur, numpho

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPTTSingLabelAudioCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[1].size(1) for x in batch]),
            dim=0, descending=True)

        max_phone_len = max([len(x[0]) for x in batch])
        max_spec_len = max([x[1].size(1) for x in batch])
        max_wav_len = max([x[2].size(1) for x in batch])
        max_pitchid_len = max([len(x[3]) for x in batch])
        max_slur_len = max([len(x[4]) for x in batch])
        max_pos_len = max([len(x[5]) for x in batch])
        max_dur_len = max([len(x[6]) for x in batch])
        max_LF0_len = max([x[7].size(1) for x in batch])
        max_durtarget_len = max([x[8].size(1) for x in batch])
        max_numpho_len = max([len(x[9]) for x in batch])

        phone_lengths = torch.LongTensor(len(batch))
        spec_lengths = torch.LongTensor(len(batch))
        wav_lengths = torch.LongTensor(len(batch))
        pitchid_lengths = torch.LongTensor(len(batch))
        slur_lengths = torch.LongTensor(len(batch))
        pos_lengths = torch.LongTensor(len(batch))
        dur_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))
        durtarget_lengths = torch.LongTensor(len(batch))
        numpho_lengths = torch.LongTensor(len(batch))

        phone_padded = torch.LongTensor(len(batch), max_phone_len)
        spec_padded = torch.FloatTensor(len(batch), batch[0][1].size(0), max_spec_len)
        wav_padded = torch.FloatTensor(len(batch), 1, max_wav_len)
        pitchid_padded = torch.LongTensor(len(batch), max_pitchid_len)
        slur_padded = torch.LongTensor(len(batch), max_slur_len)
        pos_padded = torch.LongTensor(len(batch), max_pos_len)
        dur_padded = torch.FloatTensor(len(batch), max_dur_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        durtarget_padded = torch.LongTensor(len(batch), 1, max_durtarget_len)
        numpho_padded = torch.LongTensor(len(batch), max_numpho_len)

        phone_padded.zero_()
        spec_padded.zero_()
        wav_padded.zero_()
        pitchid_padded.zero_()
        slur_padded.zero_()
        pos_padded.zero_()
        dur_padded.zero_()
        LF0_padded.zero_()
        durtarget_padded.zero_()
        numpho_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            phone = row[0]
            phone_padded[i, :phone.size(0)] = phone
            phone_lengths[i] = phone.size(0)

            spec = row[1]
            spec_padded[i, :, :spec.size(1)] = spec
            spec_lengths[i] = spec.size(1)

            wav = row[2]
            wav_padded[i, :, :wav.size(1)] = wav
            wav_lengths[i] = wav.size(1)

            pitchid = row[3]
            pitchid_padded[i, :pitchid.size(0)] = pitchid
            pitchid_lengths[i] = pitchid.size(0)

            slur = row[4]
            slur_padded[i, :slur.size(0)] = slur
            slur_lengths[i] = slur.size(0)

            pos = row[5]
            pos_padded[i, :pos.size(0)] = pos
            pos_lengths[i] = pos.size(0)

            dur = row[6]
            dur_padded[i, :dur.size(0)] = dur
            dur_lengths[i] = dur.size(0)

            LF0 = row[7]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)

            durtarget = row[8]

            durtarget_padded[i, :, :durtarget.size(1)] = durtarget
            durtarget_lengths[i] = durtarget.size(1)

            numpho = row[9]
            numpho_padded[i, :numpho.size(0)] = numpho
            numpho_lengths[i] = numpho.size(0)

        if self.return_ids:
            return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, pitchid_padded, pitchid_lengths, slur_padded, slur_lengths, pos_padded, pos_lengths, dur_padded, dur_lengths, LF0_padded, LF0_lengths, durtarget_padded, durtarget_lengths, numpho_padded, numpho_lengths, ids_sorted_decreasing
        return phone_padded, phone_lengths, spec_padded, spec_lengths, wav_padded, wav_lengths, pitchid_padded, pitchid_lengths, slur_padded, slur_lengths, pos_padded, pos_lengths, dur_padded, dur_lengths, LF0_padded, LF0_lengths, durtarget_padded, durtarget_lengths, numpho_padded, numpho_lengths,


class ASLPVocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        #self.segment_size   = hparams.segment_size

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length

        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, LF0path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        wav = self.get_audio(audiopath)
        mel = np.load(melpath)
        if(mel.shape[1] == 80):
            mel = mel.T
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        LF0 = np.load(LF0path).reshape(-1)
        LF0 = np.where(LF0>0., np.exp(LF0), 0.)
        #LF0 = np.zeros([spec.shape[1]])
            # raise
        if(LF0.shape[0] > mel.shape[1]):
            LF0 = LF0[:mel.shape[1]]
        elif(LF0.shape[0] < mel.shape[1]):
            LF0 = np.concatenate([LF0, np.zeros(mel.shape[1] - LF0.shape[0])], axis=0)

        assert LF0.shape[0] == mel.shape[1]
        if(wav.shape[1] < 8000):
            return None
        uv = np.zeros(LF0.shape, dtype=np.float32)
        uv[LF0 > 0] = 1

        LF0 = self.interp1d(LF0)
        LF0 = LF0.reshape([ 1, -1])
        uv = uv.reshape([1, -1])
        LF0 = torch.from_numpy(LF0)
        uv = torch.from_numpy(uv)
        if wav.shape[1] < 24000:
            print(LF0path)
        return (mel, wav, LF0, uv)

    def interp1d(self, pitch):
        pitch = pitch.reshape(-1)
        nonzero_ids = np.where(pitch > 0)[0]
        if len(nonzero_ids) > 1:
           interp_fn = interp1d(
               nonzero_ids,
               pitch[nonzero_ids],
               fill_value=(pitch[nonzero_ids[0]], pitch[nonzero_ids[-1]]),
               bounds_error=False,
           )
           pitch = interp_fn(np.arange(0, len(pitch)))

        return pitch.reshape(-1, 1)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)


        return audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPVocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_LF0_len = max([x[2].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        LF0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        LF0_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)
        uv_padded = torch.FloatTensor(len(batch), 1, max_LF0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        LF0_padded.zero_()
        uv_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            LF0 = row[2]
            LF0_padded[i, :, :LF0.size(1)] = LF0
            LF0_lengths[i] = LF0.size(1)

            uv = row[3]
            uv_padded[i, :, :uv.size(1)] = uv


        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, LF0_padded, LF0_lengths, uv_padded, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, LF0_padded, LF0_lengths, uv_padded


class ASLPSEVocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        # # LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]])
        # if(abs(LF0.shape[0] - spec.shape[1]) > 5):
        #     print("LF0 shape: ", LF0.shape, spec.shape)
        #     return None
        #     # raise
        # if(LF0.shape[0] > spec.shape[1]):
        #     LF0 = LF0[:spec.shape[1]]
        # elif(LF0.shape[0] < spec.shape[1]):
        #     LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        # assert LF0.shape[0] == spec.shape[1]

        # LF0 = LF0.reshape([ 1, -1])
        # LF0 = torch.from_numpy(LF0)


        source_excitation = np.load(source_excitation_path)
        if(abs(source_excitation.shape[0] - spec.shape[1] * self.hop_length) > 5 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
            # raise
        if(source_excitation.shape[0] > spec.shape[1] * self.hop_length):
            source_excitation = source_excitation[:spec.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < spec.shape[1] * self.hop_length):
            source_excitation = np.concatenate([source_excitation, np.zeros([spec.shape[1] * self.hop_length - source_excitation.shape[0], 32])], axis=0)

        assert source_excitation.shape[0] == spec.shape[1] * self.hop_length

        source_excitation = source_excitation.reshape([1, -1])
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSEVocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])

        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 32, max_source_excitation_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            harmonic_index = np.arange(1, 32 + 1)
            harmonic_index = np.expand_dims(harmonic_index, 1)
            source_excitation = source_excitation * harmonic_index
            source_excitation = source_excitation * 2 * np.pi
            source_excitation = torch.sin(source_excitation) * 0.1 + 0.002 * np.random.randn(source_excitation.shape[0], source_excitation.shape[1])

            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths

class ASLPSEVocoderv2Loader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        # # LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]])
        # if(abs(LF0.shape[0] - spec.shape[1]) > 5):
        #     print("LF0 shape: ", LF0.shape, spec.shape)
        #     return None
        #     # raise
        # if(LF0.shape[0] > spec.shape[1]):
        #     LF0 = LF0[:spec.shape[1]]
        # elif(LF0.shape[0] < spec.shape[1]):
        #     LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        # assert LF0.shape[0] == spec.shape[1]

        # LF0 = LF0.reshape([ 1, -1])
        # LF0 = torch.from_numpy(LF0)


        source_excitation = np.load(source_excitation_path)
        print("source_excitation shape: ", source_excitation.shape, spec.shape)
        if(abs(source_excitation.shape[0] - spec.shape[1] * self.hop_length) > 5 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
            # raise
        if(source_excitation.shape[0] > spec.shape[1] * self.hop_length):
            source_excitation = source_excitation[:spec.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < spec.shape[1] * self.hop_length):
            source_excitation = np.concatenate([source_excitation, np.zeros([spec.shape[1] * self.hop_length - source_excitation.shape[0], 32])], axis=0)

        assert source_excitation.shape[0] == spec.shape[1] * self.hop_length

        source_excitation = source_excitation.T
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSEVocoderv2Collate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])

        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 32, max_source_excitation_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths


class ASLPSpeechTemplateVocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        # # LF0 = np.load(LF0path)
        # LF0 = np.zeros([spec.shape[1]])
        # if(abs(LF0.shape[0] - spec.shape[1]) > 5):
        #     print("LF0 shape: ", LF0.shape, spec.shape)
        #     return None
        #     # raise
        # if(LF0.shape[0] > spec.shape[1]):
        #     LF0 = LF0[:spec.shape[1]]
        # elif(LF0.shape[0] < spec.shape[1]):
        #     LF0 = np.concatenate([LF0, np.zeros(spec.shape[1] - LF0.shape[0])], axis=0)

        # assert LF0.shape[0] == spec.shape[1]

        # LF0 = LF0.reshape([ 1, -1])
        # LF0 = torch.from_numpy(LF0)


        source_excitation = np.load(source_excitation_path)
        source_excitation = source_excitation.repeat( 32, axis=1)
        # print("source_excitation shape: ", source_excitation.shape, spec.shape)
        if(abs(source_excitation.shape[0] - spec.shape[1] * self.hop_length) > 5 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
            # raise
        if(source_excitation.shape[0] > spec.shape[1] * self.hop_length):
            source_excitation = source_excitation[:spec.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < spec.shape[1] * self.hop_length):
            source_excitation = np.concatenate([source_excitation, np.zeros([spec.shape[1] * self.hop_length - source_excitation.shape[0], 32])], axis=0)

        assert source_excitation.shape[0] == spec.shape[1] * self.hop_length

        source_excitation = source_excitation.T
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechTemplateVocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])

        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 32, max_source_excitation_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths


class ASLPSpeechTemplateUVVocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        source_excitation = np.load(source_excitation_path)

        #source_excitation = source_excitation.repeat( 32, axis=1)
        # print("source_excitation shape: ", source_excitation.shape, spec.shape)
        if(abs(source_excitation.shape[0] - spec.shape[1] * self.hop_length) > 5 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
            # raise
        if(source_excitation.shape[0] > spec.shape[1] * self.hop_length):
            source_excitation = source_excitation[:spec.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < spec.shape[1] * self.hop_length):
            source_excitation = np.concatenate([source_excitation, np.zeros([spec.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        assert source_excitation.shape[0] == spec.shape[1] * self.hop_length

        unvoice_label = - source_excitation
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([1, -1])
        unvoice_label = torch.from_numpy(unvoice_label)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == 1
        source_excitation = torch.from_numpy(source_excitation)

        return (mel, wav, source_excitation, unvoice_label)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechTemplateUVVocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths



class ASLPSpeechMultTemplateUVVocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, se_path, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, se_path, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))


    def SourceExcitation(self, F0, fs=44100, scale_factor=512, num_temp=1):
        result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)

        for _index_temp in range(1, num_temp + 1):
            current_state = 0.
            current_index = 0
            for index_frame in range(F0.shape[0]):
                if(F0[index_frame] <= 10):
                    continue
                for _index_excitation in range(1000):
                    next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
                    #if(next_excitation_index == 1):
                        #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
                    if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):
                        current_state = 0.
                        current_index = next_excitation_index + current_index
                        #print(current_index)
                        result[int(current_index), _index_temp - 1] = 1
                        #result[_index_temp - 1].append(int(current_index))
                    else:
                        current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
                        current_index = (index_frame + 1) * scale_factor
                        break
        return result

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path, F0_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        wav = wav[:, :wav.shape[1] // self.hop_length * self.hop_length]
        if(wav.shape[1] // self.hop_length > mel.shape[1]):
            wav = wav[:, :mel.shape[1] * self.hop_length]
        elif(wav.shape[1] // self.hop_length < mel.shape[1]):
            mel = mel[:, :wav.shape[1] // self.hop_length]

        source_excitation = np.load(source_excitation_path).astype(np.float32)
        if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
            # raise
        if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
            source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
            source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        assert source_excitation.shape[0] == mel.shape[1] * self.hop_length

        F0 = np.load(F0_path).reshape(-1)

        num_temp = 1

        unvoice_label = F0.reshape(-1).repeat(self.hop_length)
        unvoice_label = unvoice_label - 1
        unvoice_label = - unvoice_label
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([-1, 1])
        if(unvoice_label.shape[0] > mel.shape[1] * self.hop_length):
            unvoice_label = unvoice_label[:mel.shape[1] * self.hop_length]
        elif(unvoice_label.shape[0] < mel.shape[1] * self.hop_length):
            unvoice_label = np.concatenate([unvoice_label, np.zeros([mel.shape[1] * self.hop_length - unvoice_label.shape[0], 1])], axis=0)
        unvoice_label = torch.from_numpy(unvoice_label).reshape([1, -1])

        if(F0.shape[0] > mel.shape[1]):
            F0 = F0[:mel.shape[1]]
        elif(F0.shape[0] < mel.shape[1]):
            F0 = np.concatenate([F0, np.zeros([mel.shape[1] - F0.shape[0]])], axis=0)
        #source_excitation = self.SourceExcitation(F0)
        F0 = F0.reshape([1, -1])
        F0 = torch.from_numpy(F0)

        #if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
        #    source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        #elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
        #    source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], num_temp])], axis=0)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == num_temp
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation, unvoice_label, F0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechMultTemplateUVVocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])
        max_F0_len = max([x[4].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))
        F0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)
        F0_padded = torch.FloatTensor(len(batch), 1, max_F0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()
        F0_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

            F0 = row[4]
            F0_padded[i, :, :F0.size(1)] = F0
            F0_lengths[i] = F0.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths


class ASLPSpeechMultTemplateUV_v2_VocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, se_path, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, se_path, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))


    def SourceExcitation(self, F0, fs=44100, scale_factor=512, num_temp=1):
        result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)

        for _index_temp in range(1, num_temp + 1):
            current_state = 0.
            current_index = 0
            for index_frame in range(F0.shape[0]):
                if(F0[index_frame] <= 20):
                    current_index = (index_frame + 1) * scale_factor
                    continue
                for _index_excitation in range(1000):
                    next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
                    #if(next_excitation_index == 1):
                        #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
                    if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):
                        current_state = 0.
                        current_index = next_excitation_index + current_index
                        #print(current_index)
                        result[int(current_index), _index_temp - 1] = 1
                        #result[_index_temp - 1].append(int(current_index))
                    else:
                        current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
                        current_index = (index_frame + 1) * scale_factor
                        break
        return result

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path, F0_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        wav = wav[:, :wav.shape[1] // self.hop_length * self.hop_length]
        if(wav.shape[1] // self.hop_length > mel.shape[1]):
            wav = wav[:, :mel.shape[1] * self.hop_length]
        elif(wav.shape[1] // self.hop_length < mel.shape[1]):
            mel = mel[:, :wav.shape[1] // self.hop_length]

        # source_excitation = np.load(source_excitation_path).astype(np.float32)
        # if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
        #     print("source_excitation shape: ", source_excitation.shape, spec.shape)
        #     return None
        #     # raise
        # if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
        #     source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        # elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
        #     source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        # assert source_excitation.shape[0] == mel.shape[1] * self.hop_length

        F0 = np.load(F0_path).reshape(-1)
        if(F0.shape[0] > mel.shape[1]):
            F0 = F0[:mel.shape[1]]
        elif(F0.shape[0] < mel.shape[1]):
            F0 = np.concatenate([F0, np.zeros([mel.shape[1] - F0.shape[0]])], axis=0)

        num_temp = 1
        source_excitation = self.SourceExcitation(F0, 44100, 512, num_temp)

        unvoice_label = F0.reshape(-1).repeat(self.hop_length)
        unvoice_label = unvoice_label - 1
        unvoice_label = - unvoice_label
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([-1, 1])
        if(unvoice_label.shape[0] > mel.shape[1] * self.hop_length):
            unvoice_label = unvoice_label[:mel.shape[1] * self.hop_length]
        elif(unvoice_label.shape[0] < mel.shape[1] * self.hop_length):
            unvoice_label = np.concatenate([unvoice_label, np.zeros([mel.shape[1] * self.hop_length - unvoice_label.shape[0], 1])], axis=0)
        unvoice_label = torch.from_numpy(unvoice_label).reshape([1, -1])

        F0 = F0.reshape([1, -1])
        F0 = torch.from_numpy(F0)

        if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
        if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
           source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
           source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], num_temp])], axis=0)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == num_temp
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation, unvoice_label, F0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechMultTemplateUV_v2_VocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])
        max_F0_len = max([x[4].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))
        F0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)
        F0_padded = torch.FloatTensor(len(batch), 1, max_F0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()
        F0_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

            F0 = row[4]
            F0_padded[i, :, :F0.size(1)] = F0
            F0_lengths[i] = F0.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths

class ASLPSpeechMultTemplateUV_24k_VocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, se_path, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, se_path, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def interpolate(self, se, start_index, end_index, start_value, end_value):
        #print("inter : ", start_index, end_index, start_value, end_value)
        if(se.shape[0] <= start_index + 1 or se.shape[0] < int(end_index)):
            return se
        value_step = (end_value - start_value) / (end_index - start_index)
        se[int(start_index) + 1] = start_value + value_step
        for i in range(int(start_index) + 2, int(end_index)):
            se[i] = se[i-1] + value_step
        return se

    def SourceExcitation(self, F0, fs=44100, scale_factor=512, num_temp=1):
        result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)
        pulse_indexs = []
        for _index_temp in range(1, 2):
            current_state = 0.
            current_index = 0
            for index_frame in range(F0.shape[0]):
                if(F0[index_frame] <= 20):
                    current_index = (index_frame + 1) * scale_factor
                    continue
                for _index_excitation in range(1000):
                    next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
                    #if(next_excitation_index == 1):
                        #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
                    if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):

                        current_state = 0.
                        current_index = next_excitation_index + current_index
                        #print(current_index)
                        result[int(current_index), _index_temp - 1] = 1
                        pulse_indexs.append(int(current_index))
                        #result[_index_temp - 1].append(int(current_index))
                    else:
                        current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
                        current_index = (index_frame + 1) * scale_factor
                        break
        for i in range(len(pulse_indexs) - 1):
            result = self.interpolate(result, pulse_indexs[i], pulse_indexs[i+1], 0, 1)
        #harmonic_index = np.arange(1, 17).reshape(1, -1)
        result, _ = np.modf(result)
        return result
    # def SourceExcitation(self, F0, fs=24000, scale_factor=256, num_temp=1):
    #     result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)

    #     for _index_temp in range(1, num_temp + 1):
    #         current_state = 0.
    #         current_index = 0
    #         for index_frame in range(F0.shape[0]):
    #             if(F0[index_frame] <= 20):
    #                 current_index = (index_frame + 1) * scale_factor
    #                 continue
    #             for _index_excitation in range(1000):
    #                 next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
    #                 #if(next_excitation_index == 1):
    #                     #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
    #                 if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):
    #                     current_state = 0.
    #                     current_index = next_excitation_index + current_index
    #                     #print(current_index)
    #                     result[int(current_index), _index_temp - 1] = 1
    #                     #result[_index_temp - 1].append(int(current_index))
    #                 else:
    #                     current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
    #                     current_index = (index_frame + 1) * scale_factor
    #                     break
    #     return result

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path, F0_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        spec, wav = self.get_audio(audiopath)

        #if(not os.path.exists(melpath)):
        #    mel = spec_to_mel_torch(
        #      spec,
        #      hps.data.filter_length,
        #      hps.data.n_mel_channels,
        #      hps.data.sampling_rate,
        #      hps.data.mel_fmin,
        #      hps.data.mel_fmax
        #    )
        #else:
        #    mel = np.load(melpath).T

        mel = np.load(melpath)
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        wav = wav[:, :wav.shape[1] // self.hop_length * self.hop_length]
        if(wav.shape[1] // self.hop_length > mel.shape[1]):
            wav = wav[:, :mel.shape[1] * self.hop_length]
        elif(wav.shape[1] // self.hop_length < mel.shape[1]):
            mel = mel[:, :wav.shape[1] // self.hop_length]
        assert wav.shape[1] // self.hop_length == mel.shape[1]
        # source_excitation = np.load(source_excitation_path).astype(np.float32)
        # if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
        #     print("source_excitation shape: ", source_excitation.shape, spec.shape)
        #     return None
        #     # raise
        # if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
        #     source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        # elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
        #     source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        # assert source_excitation.shape[0] == mel.shape[1] * self.hop_length

        F0 = np.load(F0_path).reshape(-1)
        if(F0.shape[0] > mel.shape[1]):
            F0 = F0[:mel.shape[1]]
        elif(F0.shape[0] < mel.shape[1]):
            F0 = np.concatenate([F0, np.zeros([mel.shape[1] - F0.shape[0]])], axis=0)

        num_temp = 1
        source_excitation = self.SourceExcitation(F0, 24000, 256, num_temp)

        unvoice_label = F0.reshape(-1).repeat(self.hop_length)
        unvoice_label = unvoice_label - 1
        unvoice_label = - unvoice_label
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([-1, 1])
        if(unvoice_label.shape[0] > mel.shape[1] * self.hop_length):
            unvoice_label = unvoice_label[:mel.shape[1] * self.hop_length]
        elif(unvoice_label.shape[0] < mel.shape[1] * self.hop_length):
            unvoice_label = np.concatenate([unvoice_label, np.zeros([mel.shape[1] * self.hop_length - unvoice_label.shape[0], 1])], axis=0)
        unvoice_label = torch.from_numpy(unvoice_label).reshape([1, -1])

        F0 = F0.reshape([1, -1])
        F0 = torch.from_numpy(F0)

        if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, mel.shape, self.hop_length)
            return None
        if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
           source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
           source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], num_temp])], axis=0)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == num_temp
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation, unvoice_label, F0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechMultTemplateUV_24k_VocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])
        max_F0_len = max([x[4].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))
        F0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)
        F0_padded = torch.FloatTensor(len(batch), 1, max_F0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()
        F0_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

            F0 = row[4]
            F0_padded[i, :, :F0.size(1)] = F0
            F0_lengths[i] = F0.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths



class ASLPSpeechMultTemplateUV_v3_VocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, se_path, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, se_path, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))


    def SourceExcitation(self, F0, fs=44100, scale_factor=512, num_temp=1):
        result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)

        for _index_temp in range(1, num_temp + 1):
            current_state = 0.
            current_index = 0
            for index_frame in range(F0.shape[0]):
                if(F0[index_frame] <= 20):
                    current_index = (index_frame + 1) * scale_factor
                    continue
                for _index_excitation in range(1000):
                    next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
                    #if(next_excitation_index == 1):
                        #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
                    if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):
                        current_state = 0.
                        current_index = next_excitation_index + current_index
                        #print(current_index)
                        result[int(current_index), _index_temp - 1] = 1
                        #result[_index_temp - 1].append(int(current_index))
                    else:
                        current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
                        current_index = (index_frame + 1) * scale_factor
                        break
        return result

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path, F0_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        spec, wav = self.get_audio(audiopath)

        #if(not os.path.exists(melpath)):
        #    mel = spec_to_mel_torch(
        #      spec,
        #      hps.data.filter_length,
        #      hps.data.n_mel_channels,
        #      hps.data.sampling_rate,
        #      hps.data.mel_fmin,
        #      hps.data.mel_fmax
        #    )
        #else:
        #    mel = np.load(melpath).T

        mel = np.load(melpath).T
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        wav = wav[:, :wav.shape[1] // self.hop_length * self.hop_length]
        if(wav.shape[1] // self.hop_length > mel.shape[1]):
            wav = wav[:, :mel.shape[1] * self.hop_length]
        elif(wav.shape[1] // self.hop_length < mel.shape[1]):
            mel = mel[:, :wav.shape[1] // self.hop_length]

        # source_excitation = np.load(source_excitation_path).astype(np.float32)
        # if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
        #     print("source_excitation shape: ", source_excitation.shape, spec.shape)
        #     return None
        #     # raise
        # if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
        #     source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        # elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
        #     source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        # assert source_excitation.shape[0] == mel.shape[1] * self.hop_length

        F0 = np.load(F0_path).reshape(-1)
        if(F0.shape[0] > mel.shape[1]):
            F0 = F0[:mel.shape[1]]
        elif(F0.shape[0] < mel.shape[1]):
            F0 = np.concatenate([F0, np.zeros([mel.shape[1] - F0.shape[0]])], axis=0)

        num_temp = 1
        source_excitation = self.SourceExcitation(F0, 44100 // 8, 512 // 8, num_temp)

        unvoice_label = F0.reshape(-1).repeat(self.hop_length)
        unvoice_label = unvoice_label - 1
        unvoice_label = - unvoice_label
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([-1, 1])
        if(unvoice_label.shape[0] > mel.shape[1] * self.hop_length):
            unvoice_label = unvoice_label[:mel.shape[1] * self.hop_length]
        elif(unvoice_label.shape[0] < mel.shape[1] * self.hop_length):
            unvoice_label = np.concatenate([unvoice_label, np.zeros([mel.shape[1] * self.hop_length - unvoice_label.shape[0], 1])], axis=0)
        unvoice_label = torch.from_numpy(unvoice_label).reshape([1, -1])

        F0 = F0.reshape([1, -1])
        F0 = torch.from_numpy(F0)

        if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length // 8) > 10 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
        if(source_excitation.shape[0] > mel.shape[1] * self.hop_length // 8):
           source_excitation = source_excitation[:mel.shape[1] * self.hop_length // 8]
        elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length // 8):
           source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length // 8 - source_excitation.shape[0], num_temp])], axis=0)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == num_temp
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation, unvoice_label, F0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechMultTemplateUV_v3_VocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])
        max_F0_len = max([x[4].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))
        F0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)
        F0_padded = torch.FloatTensor(len(batch), 1, max_F0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()
        F0_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

            F0 = row[4]
            F0_padded[i, :, :F0.size(1)] = F0
            F0_lengths[i] = F0.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths


class ASLPSpeechMultTemplateUV_v4_VocoderLoader(torch.utils.data.Dataset):
    """
        1) loads audio, text pairs
        2) normalizes text and converts them to sequences of integers
        3) computes spectrograms from audio files.
    """
    def __init__(self, audiopaths_and_text, hparams):
        self.audiopaths_and_text = load_filepaths_and_text(audiopaths_and_text)
        self.text_cleaners  = hparams.text_cleaners
        self.max_wav_value  = hparams.max_wav_value
        self.sampling_rate  = hparams.sampling_rate
        self.filter_length  = hparams.filter_length
        self.hop_length     = hparams.hop_length
        self.win_length     = hparams.win_length
        self.sampling_rate  = hparams.sampling_rate

        self.cleaned_text = getattr(hparams, "cleaned_text", False)

        self.add_blank = hparams.add_blank
        self.min_text_len = getattr(hparams, "min_text_len", 1)
        self.max_text_len = getattr(hparams, "max_text_len", 300)

        random.seed(1234)
        random.shuffle(self.audiopaths_and_text)
        self._filter()


    def _filter(self):
        """
        Filter text & store spec lengths
        """
        # Store spectrogram lengths for Bucketing
        # wav_length ~= file_size / (wav_channels * Bytes per dim) = file_size / (1 * 2)
        # spec_length = wav_length // hop_length
        print("before filter : ", len(self.audiopaths_and_text))
        audiopaths_new = []
        lengths = []
        # print("input: ", self.audiopaths_and_text[0])
        for melpath, audiopath, se_path, LF0path in self.audiopaths_and_text:
            audiopaths_new.append([melpath, audiopath, se_path, LF0path])
            lengths.append(os.path.getsize(audiopath) // (2 * self.hop_length))
        self.audiopaths_and_text = audiopaths_new
        self.lengths = lengths
        print("after filter : ", len(self.audiopaths_and_text))

    def interpolate(self, se, start_index, end_index, start_value, end_value):
        #print("inter : ", start_index, end_index, start_value, end_value)
        if(se.shape[0] <= start_index + 1 or se.shape[0] < int(end_index)):
            return se
        value_step = (end_value - start_value) / (end_index - start_index)
        se[int(start_index) + 1] = start_value + value_step
        for i in range(int(start_index) + 2, int(end_index)):
            se[i] = se[i-1] + value_step
        return se

    def SourceExcitation(self, F0, fs=44100, scale_factor=512, num_temp=1):
        result = np.zeros([F0.shape[0] * scale_factor, num_temp], dtype=np.float32)
        pulse_indexs = []
        for _index_temp in range(1, 2):
            current_state = 0.
            current_index = 0
            for index_frame in range(F0.shape[0]):
                if(F0[index_frame] <= 20):
                    current_index = (index_frame + 1) * scale_factor
                    continue
                for _index_excitation in range(1000):
                    next_excitation_index = max((1 - current_state) * fs // (F0[index_frame] * _index_temp), 1)
                    #if(next_excitation_index == 1):
                        #print(next_excitation_index, current_state, F0[index_frame], _index_temp)
                    if(next_excitation_index + current_index < (index_frame + 1) * scale_factor):

                        current_state = 0.
                        current_index = next_excitation_index + current_index
                        #print(current_index)
                        result[int(current_index), _index_temp - 1] = 1
                        pulse_indexs.append(int(current_index))
                        #result[_index_temp - 1].append(int(current_index))
                    else:
                        current_state = ((index_frame + 1) * scale_factor - current_index) / next_excitation_index
                        current_index = (index_frame + 1) * scale_factor
                        break
        for i in range(len(pulse_indexs) - 1):
            result = self.interpolate(result, pulse_indexs[i], pulse_indexs[i+1], 0, 1)
        #harmonic_index = np.arange(1, 17).reshape(1, -1)
        result, _ = np.modf(result)
        return result

    def get_audio_text_pair(self, audiopath_and_text):
        # separate filename and text

        melpath, audiopath, source_excitation_path, F0_path = audiopath_and_text[0], audiopath_and_text[1], audiopath_and_text[2], audiopath_and_text[3]
        spec, wav = self.get_audio(audiopath)

        mel = np.load(melpath).T
        assert mel.shape[0] == 80
        mel = mel.reshape([ 80, -1])
        mel = torch.from_numpy(mel)

        wav = wav[:, :wav.shape[1] // self.hop_length * self.hop_length]
        if(wav.shape[1] // self.hop_length > mel.shape[1]):
            wav = wav[:, :mel.shape[1] * self.hop_length]
        elif(wav.shape[1] // self.hop_length < mel.shape[1]):
            mel = mel[:, :wav.shape[1] // self.hop_length]

        # source_excitation = np.load(source_excitation_path).astype(np.float32)
        # if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length) > 10 * self.hop_length):
        #     print("source_excitation shape: ", source_excitation.shape, spec.shape)
        #     return None
        #     # raise
        # if(source_excitation.shape[0] > mel.shape[1] * self.hop_length):
        #     source_excitation = source_excitation[:mel.shape[1] * self.hop_length]
        # elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length):
        #     source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length - source_excitation.shape[0], 1])], axis=0)

        # assert source_excitation.shape[0] == mel.shape[1] * self.hop_length

        F0 = np.load(F0_path).reshape(-1)
        if(F0.shape[0] > mel.shape[1]):
            F0 = F0[:mel.shape[1]]
        elif(F0.shape[0] < mel.shape[1]):
            F0 = np.concatenate([F0, np.zeros([mel.shape[1] - F0.shape[0]])], axis=0)

        num_temp = 1
        source_excitation = self.SourceExcitation(F0, 44100 // 8, 512 // 8, num_temp)

        unvoice_label = F0.reshape(-1).repeat(self.hop_length)
        unvoice_label = unvoice_label - 1
        unvoice_label = - unvoice_label
        unvoice_label = np.minimum(unvoice_label, 1.)
        unvoice_label = np.maximum(unvoice_label, 0.)
        unvoice_label = unvoice_label.reshape([-1, 1])
        if(unvoice_label.shape[0] > mel.shape[1] * self.hop_length):
            unvoice_label = unvoice_label[:mel.shape[1] * self.hop_length]
        elif(unvoice_label.shape[0] < mel.shape[1] * self.hop_length):
            unvoice_label = np.concatenate([unvoice_label, np.zeros([mel.shape[1] * self.hop_length - unvoice_label.shape[0], 1])], axis=0)
        unvoice_label = torch.from_numpy(unvoice_label).reshape([1, -1])

        F0 = F0.reshape([1, -1])
        F0 = torch.from_numpy(F0)

        if(abs(source_excitation.shape[0] - mel.shape[1] * self.hop_length // 8) > 10 * self.hop_length):
            print("source_excitation shape: ", source_excitation.shape, spec.shape)
            return None
        if(source_excitation.shape[0] > mel.shape[1] * self.hop_length // 8):
           source_excitation = source_excitation[:mel.shape[1] * self.hop_length // 8]
        elif(source_excitation.shape[0] < mel.shape[1] * self.hop_length // 8):
           source_excitation = np.concatenate([source_excitation, np.zeros([mel.shape[1] * self.hop_length // 8 - source_excitation.shape[0], num_temp])], axis=0)

        source_excitation = source_excitation.T
        assert source_excitation.shape[0] == num_temp
        source_excitation = torch.from_numpy(source_excitation)
        return (mel, wav, source_excitation, unvoice_label, F0)

    def get_audio(self, filename):
        audio, sampling_rate = load_wav_to_torch(filename)
        if sampling_rate != self.sampling_rate:
            raise ValueError("{} {} SR doesn't match target {} SR".format(
                sampling_rate, self.sampling_rate))
        audio_norm = audio / self.max_wav_value
        audio_norm = audio_norm.unsqueeze(0)
        spec_filename = filename.replace(".wav", ".spec.pt")
        if os.path.exists(spec_filename):
            spec = torch.load(spec_filename)
        else:
            spec = spectrogram_torch(audio_norm, self.filter_length,
                self.sampling_rate, self.hop_length, self.win_length,
                center=False)
            spec = torch.squeeze(spec, 0)
            torch.save(spec, spec_filename)
        return spec, audio_norm

    def __getitem__(self, index):
        return self.get_audio_text_pair(self.audiopaths_and_text[index])

    def __len__(self):
        return len(self.audiopaths_and_text)


class ASLPSpeechMultTemplateUV_v4_VocoderCollate():
    """ Zero-pads model inputs and targets
    """
    def __init__(self, return_ids=False):
        self.return_ids = return_ids

    def __call__(self, batch):
        """Collate's training batch from normalized text and aduio
        PARAMS
        ------
        batch: [text_normalized, spec_normalized, wav_normalized]
        """
        # Right zero-pad all one-hot text sequences to max input length
        batch = [b for b in batch if b is not None]

        _, ids_sorted_decreasing = torch.sort(
            torch.LongTensor([x[0].size(1) for x in batch]),
            dim=0, descending=True)


        max_mel_len = max([x[0].size(1) for x in batch])
        max_audio_len = max([x[1].size(1) for x in batch])
        max_source_excitation_len = max([x[2].size(1) for x in batch])
        max_unvoice_label_len = max([x[3].size(1) for x in batch])
        max_F0_len = max([x[4].size(1) for x in batch])


        mel_lengths = torch.LongTensor(len(batch))
        audio_lengths = torch.LongTensor(len(batch))
        source_excitation_lengths = torch.LongTensor(len(batch))
        unvoice_label_lengths = torch.LongTensor(len(batch))
        F0_lengths = torch.LongTensor(len(batch))

        mel_padded = torch.FloatTensor(len(batch), 80, max_mel_len)
        audio_padded = torch.FloatTensor(len(batch), 1, max_audio_len)
        source_excitation_padded = torch.FloatTensor(len(batch), 1, max_source_excitation_len)
        unvoice_label_padded = torch.FloatTensor(len(batch), 1, max_unvoice_label_len)
        F0_padded = torch.FloatTensor(len(batch), 1, max_F0_len)

        mel_padded.zero_()
        audio_padded.zero_()
        source_excitation_padded.zero_()
        unvoice_label_padded.zero_()
        F0_padded.zero_()

        for i in range(len(ids_sorted_decreasing)):
            row = batch[ids_sorted_decreasing[i]]

            mel = row[0]
            mel_padded[i, :, :mel.size(1)] = mel
            mel_lengths[i] = mel.size(1)

            audio = row[1]
            audio_padded[i, :, :audio.size(1)] = audio
            audio_lengths[i] = audio.size(1)

            source_excitation = row[2]
            source_excitation_padded[i, :, :source_excitation.size(1)] = source_excitation
            source_excitation_lengths[i] = source_excitation.size(1)

            unvoice_label = row[3]
            unvoice_label_padded[i, :, :unvoice_label.size(1)] = unvoice_label
            unvoice_label_lengths[i] = unvoice_label.size(1)

            F0 = row[4]
            F0_padded[i, :, :F0.size(1)] = F0
            F0_lengths[i] = F0.size(1)

        if self.return_ids:
            return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths, ids_sorted_decreasing
        return mel_padded, mel_lengths, audio_padded, audio_lengths, source_excitation_padded, source_excitation_lengths, unvoice_label_padded, unvoice_label_lengths, F0_padded, F0_lengths




class DistributedBucketSampler(torch.utils.data.distributed.DistributedSampler):
    """
    Maintain similar input lengths in a batch.
    Length groups are specified by boundaries.
    Ex) boundaries = [b1, b2, b3] -> any batch is included either {x | b1 < length(x) <=b2} or {x | b2 < length(x) <= b3}.

    It removes samples which are not included in the boundaries.
    Ex) boundaries = [b1, b2, b3] -> any x s.t. length(x) <= b1 or length(x) > b3 are discarded.
    """
    def __init__(self, dataset, batch_size, boundaries, num_replicas=None, rank=None, shuffle=True):
        super().__init__(dataset, num_replicas=num_replicas, rank=rank, shuffle=shuffle)
        self.lengths = dataset.lengths
        self.batch_size = batch_size
        self.boundaries = boundaries

        self.buckets, self.num_samples_per_bucket = self._create_buckets()
        self.total_size = sum(self.num_samples_per_bucket)
        self.num_samples = self.total_size // self.num_replicas

    def _create_buckets(self):
        buckets = [[] for _ in range(len(self.boundaries) - 1)]
        for i in range(len(self.lengths)):
            length = self.lengths[i]
            idx_bucket = self._bisect(length)
            if idx_bucket != -1:
                buckets[idx_bucket].append(i)

        for i in range(len(buckets) - 1, 0, -1):
            if len(buckets[i]) == 0:
                buckets.pop(i)
                self.boundaries.pop(i+1)

        num_samples_per_bucket = []
        for i in range(len(buckets)):
            len_bucket = len(buckets[i])
            total_batch_size = self.num_replicas * self.batch_size
            rem = (total_batch_size - (len_bucket % total_batch_size)) % total_batch_size
            num_samples_per_bucket.append(len_bucket + rem)
        return buckets, num_samples_per_bucket

    def __iter__(self):
      # deterministically shuffle based on epoch
      g = torch.Generator()
      g.manual_seed(self.epoch)

      indices = []
      if self.shuffle:
          for bucket in self.buckets:
              indices.append(torch.randperm(len(bucket), generator=g).tolist())
      else:
          for bucket in self.buckets:
              indices.append(list(range(len(bucket))))

      batches = []
      for i in range(len(self.buckets)):
          bucket = self.buckets[i]
          len_bucket = len(bucket)
          ids_bucket = indices[i]
          num_samples_bucket = self.num_samples_per_bucket[i]

          # add extra samples to make it evenly divisible
          rem = num_samples_bucket - len_bucket
          ids_bucket = ids_bucket + ids_bucket * (rem // len_bucket) + ids_bucket[:(rem % len_bucket)]

          # subsample
          ids_bucket = ids_bucket[self.rank::self.num_replicas]

          # batching
          for j in range(len(ids_bucket) // self.batch_size):
              batch = [bucket[idx] for idx in ids_bucket[j*self.batch_size:(j+1)*self.batch_size]]
              batches.append(batch)

      if self.shuffle:
          batch_ids = torch.randperm(len(batches), generator=g).tolist()
          batches = [batches[i] for i in batch_ids]
      self.batches = batches

      assert len(self.batches) * self.batch_size == self.num_samples
      return iter(self.batches)

    def _bisect(self, x, lo=0, hi=None):
      if hi is None:
          hi = len(self.boundaries) - 1

      if hi > lo:
          mid = (hi + lo) // 2
          if self.boundaries[mid] < x and x <= self.boundaries[mid+1]:
              return mid
          elif x <= self.boundaries[mid]:
              return self._bisect(x, lo, mid)
          else:
              return self._bisect(x, mid + 1, hi)
      else:
          return -1

    def __len__(self):
        return self.num_samples // self.batch_size
