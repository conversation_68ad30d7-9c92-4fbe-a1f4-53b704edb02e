

import glob
import os
import re
import matplotlib
import collections
import torch
import logging
import numpy as np
from logging import handlers
from torch.nn.utils import weight_norm
from ruamel.yaml import YAML as yaml
#import yaml
from utils.hparam import HParams
matplotlib.use("Agg")
import matplotlib.pylab as plt

def str2bool(v):
    if v.lower() in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v.lower() in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise ValueError('Unsupported value encountered.')
 

class YParams(HParams):

    def __init__(self, yaml_file):
        if not os.path.exists(yaml_file):
            raise IOError("yaml file: {} is not existed".format(yaml_file))
        super().__init__()
        self.d = collections.OrderedDict()
        with open(yaml_file) as fp:
            for _, v in yaml().load(fp).items():
                for k1, v1 in v.items():
                    try:
                        if self.get(k1):
                            self.set_hparam(k1, v1)
                        else:
                            self.add_hparam(k1, v1)
                        self.d[k1] = v1
                    except Exception:
                        import traceback
                        print(traceback.format_exc())

    # @property
    def get_elements(self):
        return self.d.items()

class ValueWindow():
    def __init__(self, window_size=100):
        self._window_size = window_size
        self._values = []
 
    def append(self, x):
        self._values = self._values[-(self._window_size - 1):] + [x]
 
    @property
    def sum(self):
        return sum(self._values)
 
    @property
    def count(self):
        return len(self._values)
 
    @property
    def average(self):
        return self.sum / max(1, self.count)
 
    def reset(self):
        self._values = []


def num_params(net) :
    parameters = filter(lambda p: p.requires_grad, net.parameters())
    num_params = sum([np.prod(p.size()) for p in parameters]) / 1_000_000
    return num_params


def plot_spectrogram(spectrogram):
    fig, ax = plt.subplots(figsize=(10, 2))
    im = ax.imshow(spectrogram, aspect="auto", origin="lower",
                   interpolation='none')
    plt.colorbar(im, ax=ax)

    fig.canvas.draw()
    plt.close()

    return fig


def load_generator(checkpoint_dir, generator, pqmf_conv=None, device="cpu"):
    checkpoint_file = os.path.join(checkpoint_dir, 'checkpoint')
    if os.path.exists(checkpoint_file):
        checkpoints = open(checkpoint_file).readline().strip().split("\t")
        generator_path = os.path.join(checkpoint_dir,
                                       "{}".format(checkpoints[0].strip()))
        
        print("Restoring from {}".format(generator_path))
        generator_dict = torch.load(generator_path, map_location=device)
        generator.load_state_dict(generator_dict["generator"])
        if pqmf_conv is not None:
            pqmf_conv.load_state_dict(generator_dict["pqmf_conv"])
    else:
        raise IOError(f"{checkpoint_file} doesn't found.")


def search_and_remove(checkpoint_dir, max_to_keep, prefix):
    for item in prefix:
        pattern = os.path.join(checkpoint_dir, item)
        ckpt_list = glob.glob(pattern)
        ckpt_list = sorted(ckpt_list, key=str2int)
        for i in range(0, len(ckpt_list) - max_to_keep):
            os.remove(ckpt_list[i])


def str2int(string):
    def tryint(s):
        try:
            return int(s)
        except ValueError:
            return s
    return [tryint(sub_str) for sub_str in re.split('([0-9]+)', string)]

class Logger(object):
    def __init__(self, filename, level='info', when='D', backCount=10,
                 fmt = '%(asctime)s : %(message)s'):
        self.level_relations = {
            'debug':logging.DEBUG,
            'info':logging.INFO,
            'warning':logging.WARNING,
            'error':logging.ERROR,
            'crit':logging.CRITICAL
        }
        if level == "debug":
            fmt = '%(asctime)s - %(pathname)s[line:%(lineno)d] - %(levelname)s: %(message)s'
        self.logger = logging.getLogger(filename)
        format_str = logging.Formatter(fmt)
        self.logger.setLevel(self.level_relations.get(level))
        sh = logging.StreamHandler()
        sh.setFormatter(format_str) 
        th = handlers.TimedRotatingFileHandler(filename=filename,
            when=when,
            backupCount=backCount,
            encoding='utf-8')
        th.setFormatter(format_str)
        self.logger.addHandler(sh)
        self.logger.addHandler(th)
        self.logger.info("==========================New Starting Here==============================")

def _pad1d(seq, max_len, constant_values=0):
    return np.pad(seq, (0, max_len - len(seq)), mode='constant', constant_values=constant_values)
def _pad2d(seq, max_len, constant_values=0):
    return np.pad(seq, ((0, max_len - len(seq)), (0, 0)), mode='constant', constant_values=constant_values)
