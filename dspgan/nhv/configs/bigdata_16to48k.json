{"train": {"log_interval": 1000, "eval_interval": 20000, "seed": 1234, "epochs": 20000, "learning_rate": 0.0002, "betas": [0.8, 0.99], "eps": 1e-09, "batch_size": 8, "fp16_run": false, "lr_decay": 0.999875, "segment_size": 24000, "init_lr_ratio": 1, "warmup_epochs": 0, "c_mel": 45, "c_kl": 1.0}, "data": {"min_db": -115, "max_abs_value": 1, "min_level_db": -115, "ref_level_db": 20, "training_files": "/home/<USER>/songkun/workspace/vocoder/nhv_new/filelists_bigdata_16kto48k/train.list", "validation_files": "/home/<USER>/songkun/workspace/vocoder/nhv_new/filelists_bigdata/val.list", "text_cleaners": ["english_cleaners2"], "max_wav_value": 32768.0, "sampling_rate": 48000, "filter_length": 4096, "hop_length": 600, "win_length": 2400, "n_mel_channels": 80, "mel_fmin": 0, "mel_fmax": null, "add_blank": false, "n_speakers": 0, "cleaned_text": true}, "model": {"inter_channels": 192, "hidden_channels": 192, "filter_channels": 768, "n_heads": 2, "n_layers": 6, "kernel_size": 3, "p_dropout": 0.1, "resblock": "1", "resblock_kernel_sizes": [3, 7, 11], "resblock_dilation_sizes": [[1, 3, 5], [1, 3, 5], [1, 3, 5]], "upsample_rates": [10, 5, 4, 3], "upsample_initial_channel": 512, "upsample_kernel_sizes": [20, 10, 8, 6], "n_layers_q": 3, "use_spectral_norm": false}}