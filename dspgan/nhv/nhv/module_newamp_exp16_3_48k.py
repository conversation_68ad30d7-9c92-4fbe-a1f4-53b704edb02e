#! /usr/bin/env python
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright (c) 2021 <PERSON><PERSON><PERSON>YASHI <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""

import math
import torch.fft
import torch
import torch.nn as nn
import torch.nn.functional as F
from .layer import Conv1d, ConvLayers
import time

def scale_function(x):
    return 2 * torch.sigmoid(x)**(math.log(10)) + 1e-7

class NHV2(nn.Module):
   def __init__(self, indim, kdim, harm_outdim, noise_outdim, lf0_outdim):
       super(NHV2, self).__init__()
       self.m = nn.Sequential(
           nn.ReflectionPad1d(1),
           nn.Conv1d(indim, kdim, kernel_size=3),
           nn.LeakyReLU(0.2, True),
           nn.ReflectionPad1d(1),
           nn.Conv1d(kdim, kdim, kernel_size=3, groups=8),
           nn.LeakyReLU(0.2, True),
           nn.ReflectionPad1d(1),
           nn.Conv1d(kdim, kdim, kernel_size=3, groups=8),
           nn.LeakyReLU(0.2, True),
           nn.ReflectionPad1d(1),
           nn.Conv1d(kdim, kdim, kernel_size=3)
       )

       self.gru = nn.GRU(kdim, kdim, batch_first=True)
       self.harm_linear = nn.Linear(kdim, harm_outdim)

   def forward(self, indata):
       indata = indata.transpose(1, 2)
       x = self.m(indata)
       x, _ = self.gru(x.transpose(1, 2))

       return self.harm_linear(x).transpose(1, 2)

class CCepLTVFilter(nn.Module):
    def __init__(
        self,
        in_channels,
        conv_channels=256,
        ccep_size=222,
        kernel_size=3,
        dilation_size=1,
        group_size=8,
        fft_size=1024,
        hop_size=200,
        n_ltv_layers=3,
        n_ltv_postfilter_layers=1,
        use_causal=False,
        conv_type="original",
        feat2linear_fn=None,
        ltv_postfilter_type="conv",
        ltv_postfilter_kernel_size=128,
    ):
        super().__init__()
        self.fft_size = fft_size
        self.hop_size = hop_size
        self.window_size = hop_size * 2
        self.ccep_size = ccep_size
        self.use_causal = use_causal
        self.feat2linear_fn = feat2linear_fn
        self.ltv_postfilter_type = ltv_postfilter_type
        self.ltv_postfilter_kernel_size = ltv_postfilter_kernel_size
        self.n_ltv_postfilter_layers = n_ltv_postfilter_layers

        win_norm = self.window_size // (hop_size * 2)  # only for hanning window
        # periodic must be True to become OLA 1
        win = torch.hann_window(self.window_size, periodic=True) / win_norm
        self.conv = ConvLayers(
            in_channels=in_channels,
            conv_channels=conv_channels,
            out_channels=ccep_size,
            kernel_size=kernel_size,
            dilation_size=dilation_size,
            group_size=group_size,
            n_conv_layers=n_ltv_layers,
            use_causal=use_causal,
            conv_type=conv_type,
        )
        self.ltv_postfilter_fn = self._get_ltv_postfilter_fn()

        idx = torch.arange(1, ccep_size // 2 + 1).float()
        quef_norm = torch.cat([torch.flip(idx, dims=[-1]), idx], dim=-1)
        self.padding = (self.fft_size - self.ccep_size) // 2
        self.register_buffer("quef_norm", quef_norm)
        self.register_buffer("win", win)

    def forward(self, x, z):
        """
        x: B, T, D
        z: B, 1, T * hop_size
        """
        # inference complex cepstrum
        start = time.time()
        ccep = self.conv(x)
        end = time.time()
        ccep = ccep / self.quef_norm

        # apply LTV filter and overlap
        log_mag = None if self.feat2linear_fn is None else self.feat2linear_fn(x)
        start = time.time()
        y = self._ccep2impulse(ccep, ref=log_mag)
        end = time.time()
        start = time.time()
        z = self._conv_impulse(z, y)
        end = time.time()
        start = time.time()
        z = self._ola(z)
        end = time.time()
        if self.ltv_postfilter_fn is not None:
            z = self.ltv_postfilter_fn(z.transpose(1, 2)).transpose(1, 2)
        return z

    def _apply_ref_mag(self, real, ref):
        # TODO(k2kobayashi): it requires to consider following line.
        # this mask eliminates very small amplitude values (-100).
        # ref = ref * (ref > -100)
        real[..., : self.fft_size // 2 + 1] += ref
        real[..., self.fft_size // 2 :] += torch.flip(ref[..., 1:], dims=[-1])
        return real

    def _ccep2impulse(self, ccep, ref=None):
        ccep = F.pad(ccep, (self.padding, self.padding))
        y = torch.fft.fft(ccep, n=self.fft_size, dim=-1)
        # NOTE(k2kobayashi): we assume intermediate log amplitude as 10log10|mag|
        if ref is not None:
            y.real = self._apply_ref_mag(y.real, ref)
        # logarithmic to linear
        mag, phase = torch.pow(10, y.real / 10), y.imag
        real, imag = mag * torch.cos(phase), mag * torch.sin(phase)
        y = torch.fft.ifft(torch.complex(real, imag), n=self.fft_size + 1, dim=-1)
        #print("y: ", y.shape)
        return y.real

    def _conv_impulse(self, z, y):
        #print("melcondition: ", y.shape)
        #print("z: ", z.shape)
        #print("z: ", z[:, :, :1000])
        #plt.plot(z.squeeze()[2560:2560*5].float().numpy())
        #plt.savefig("z.png")
        # print("impulse 0: ", z.shape, y.shape,  self.window_size, self.hop_size)
        # (B, T * hop_size + hop_size)
        # z = F.pad(z, (self.hop_size // 2, self.hop_size // 2)).squeeze(1)
        z = F.pad(z, (self.hop_size, 0)).squeeze(1)
        #print("impulse 1: ", z.shape, y.shape,  self.window_size, self.hop_size)
        z = z.unfold(-1, self.window_size, step=self.hop_size)  # (B, T, window_size)
        z = F.pad(z, (self.fft_size // 2, self.fft_size // 2))
        z = z.unfold(-1, self.fft_size + 1, step=1)  # (B, T, window_size, fft_size + 1)
        #for i in range(10):
        #    print("z: ", z[:, :, 50*i:50*(i+1), :].max())
        # y: (B, T, fft_size + 1) -> (B, T, fft_size + 1, 1)
        # z: (B, T, window_size, fft_size + 1)
        # output: (B, T, window_size)
        #print("impulse shape", z.shape, y.unsqueeze(-1).shape)
        start = time.time()
        #print("z_output: ", z.shape)
        #print("z: ", z.to_sparse())
        output = torch.matmul(z, y.unsqueeze(-1)).squeeze(-1)
        #print("result: ", output.shape)
        end = time.time()
        return output

    def _conv_impulse_old(self, z, y):
        z = F.pad(z, (self.window_size // 2 - 1, self.window_size // 2)).squeeze(1)
        z = z.unfold(-1, self.window_size, step=self.hop_size)  # (B, 1, T, window_size)

        z = F.pad(z, (self.fft_size // 2 - 1, self.fft_size // 2))
        z = z.unfold(-1, self.fft_size, step=1)  # (B, 1, T, window_size, fft_size)

        # z = matmul(z, y) -> (B, 1, T, window_size) where
        # z: (B, 1, T, window_size, fft_size)
        # y: (B, T, fft_size) -> (B, 1, T, fft_size, 1)
        z = torch.matmul(z, y.unsqueeze(-1)).squeeze(-1)
        return z

    def _ola(self, z):
        z = z * self.win
        l, r = torch.chunk(z, 2, dim=-1)  # (B, 1, T, window_size // 2)
        z = l + torch.roll(r, 1, dims=-2)  # roll a frame of right side
        z = z.reshape(z.size(0), 1, -1)
        return z

    def _get_ltv_postfilter_fn(self):
        if self.ltv_postfilter_type == "ddsconv":
            fn = ConvLayers(
                in_channels=1,
                conv_channels=64,
                out_channels=1,
                kernel_size=5,
                dilation_size=2,
                n_conv_layers=self.n_ltv_postfilter_layers,
                use_causal=self.use_causal,
                conv_type="ddsconv",
            )
        elif self.ltv_postfilter_type == "conv":
            fn = Conv1d(
                in_channels=1,
                out_channels=1,
                kernel_size=self.ltv_postfilter_kernel_size,
                use_causal=self.use_causal,
            )
        elif self.ltv_postfilter_type is None:
            fn = None
        else:
            raise ValueError(f"Invalid ltv_postfilter_type: {self.ltv_postfilter_type}")
        return fn


class SinusoidsGenerator(nn.Module):
    def __init__(
        self,
        hop_size,
        fs=16000,
        harmonic_amp=0.1,
        n_harmonics=200,
        use_uvmask=True,
    ):
        super().__init__()
        self.fs = fs
        self.harmonic_amp = harmonic_amp
        self.upsample = nn.Upsample(scale_factor=hop_size, mode="nearest")
        self.upsample_2 = nn.Upsample(scale_factor=600, mode='nearest')
        self.use_uvmask = use_uvmask
        self.n_harmonics = n_harmonics
        harmonics = torch.arange(1, self.n_harmonics + 1).unsqueeze(-1)
        self.lc = NHV2(80, 512, n_harmonics, 65, 2)
        self.register_buffer("harmonics", harmonics)
        harmonics_pitch = harmonics[0, :].unsqueeze(0)
        self.register_buffer("harmonics_pitch", harmonics_pitch)


    def forward(self, conditions, cf0, uv):
        con_mask = torch.ones_like(conditions)
        #con_mask[:, :, 40:80] = 0
        conditions = conditions * con_mask
        amplitudes = self.lc(conditions)
        amplitudes = scale_function(amplitudes)
        amplitudes = self.remove_above_nyquist(amplitudes, cf0)
        #amplitudes[:, 20:, :] = 0
        #harm_param = scale_function(harm_param)
        # print("harm_param_new: ", harm_param.max(), harm_param.min())
        #total_amp, amplitudes = harm_param[:, :1, :], harm_param[:, 1:, :]
        # print("total_amp ", total_amp.max(), total_amp.min())
        # print("amplitudes ", amplitudes.max(), amplitudes.min())
        #amplitudes = self.remove_above_nyquist(amplitudes, cf0)
        # print("after_amp: ", amplitudes.max(), amplitudes.min())
        #print("amplitudes_before: ", amplitudes.shape)
        #print("amplitudes_before: ", amplitudes.max(), amplitudes.min())
        amplitudes /= amplitudes.sum(dim=1, keepdim=True)
        # print("after_after_amp: ", amplitudes.max(), amplitudes.min())
        # amplitudes *= total_amp

        uv_tmp = uv
        f0, uv = self.upsample(cf0.transpose(1, 2)), self.upsample(uv.transpose(1, 2))
        f0_2, uv_2 = self.upsample_2(cf0.transpose(1, 2)), self.upsample_2(uv_tmp.transpose(1, 2))
        amplitudes = self.upsample(amplitudes)
        harmonic = self.generate_sinusoids(f0, uv, amplitudes).reshape(cf0.size(0), 1, -1)
        harmonic_pitch = self.generate_sinusoids_pitch(f0_2, uv_2).reshape(cf0.size(0), 1, -1)
        #print("harmonic: ", harmonic.max(), harmonic.min())
        #return self.harmonic_amp * harmonic
        return harmonic, harmonic_pitch
    def remove_above_nyquist(self, amplitudes, pitch):
        pitch_with_harmonics = pitch.transpose(1, 2) * self.harmonics
        # print("harm: ", pitch_with_harmonics.max(), pitch_with_harmonics.min())
        mask = (pitch_with_harmonics < self.fs / 2.0).float() + 1e-4
        # print()

        return amplitudes * mask

    def generate_sinusoids_pitch(self, f0, uv):
        mask = self.anti_aliacing_mask(f0 * self.harmonics_pitch)
        rads = f0.cumsum(dim=-1) * 2.0 * math.pi / 48000 * self.harmonics_pitch
        harmonic = torch.cos(rads) * mask
        #harmonic = torch.sum(torch.cos(rads) * mask, dim=1, keepdim=True)
        if self.use_uvmask:
            harmonic = uv * harmonic
        return harmonic

    def generate_sinusoids(self, f0, uv, amplitudes):
        #print("f0: ", f0[:, :, :100])
        #print("uv: ", uv[:, :, :100])
        #print(self.harmonics[ :100])
        #mask = self.anti_aliacing_mask(f0 * self.harmonics)
        #print("mask: ", mask[:, :, :1000])
        rads = f0.cumsum(dim=-1) * 2.0 * math.pi / self.fs * self.harmonics
        #print("rad: ", torch.cos(rads).max(), torch.cos(rads).min())
        #print("amplitudes: ", amplitudes.max(), amplitudes.min())
        #print("cos: ", torch.cos(rads)[:, :, :1000])
        #print("amp_last: ", amplitudes.max(), amplitudes.min())
        # print("mask: ", mask.max(), mask.min())
        harmonic = torch.sum(torch.cos(rads) * amplitudes, dim=1, keepdim=True)
        if self.use_uvmask:
            #print(harmonic.max(), harmonic.min())
            #print("uv: ", uv)
            #print("uv: ", uv)
            harmonic = uv * harmonic

        #print("harmonic: ", harmonic.shape, harmonic.max(), harmonic.min())
        #harmonic = harmonic.double()
        #harmonic = torch.where(((harmonic > 0.1) | (harmonic<-0.1)), harmonic, 0.)
        #harmonic = harmonic.float()
        #print(harmonic.to_sparse())
        return harmonic

    def anti_aliacing_mask(self, f0_with_harmonics, use_soft_mask=False):
        if use_soft_mask:
            return torch.sigmoid(-(f0_with_harmonics - 48000 / 2.0))
        else:
            return (f0_with_harmonics < 48000 / 2.0).float()
