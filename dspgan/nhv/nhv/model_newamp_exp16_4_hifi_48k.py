#! /usr/bin/env python
# -*- coding: utf-8 -*-
# vim:fenc=utf-8
#
# Copyright (c) 2021 <PERSON><PERSON><PERSON>YASHI <<EMAIL>>
#
# Distributed under terms of the MIT license.

"""

"""
from torch.nn import ConvTranspose1d
import joblib
import torch
import torch.nn as nn
import torch.nn.functional as F
from .layer import Conv1d, ConvLayers
from .module_newamp_exp16_3_48k import CCepLTVFilter, SinusoidsGenerator
from .preprocess import LogMelSpectrogram2LogMagnitude, LogMelSpectrogramScaler
import time
from dspgan.nhv.pqmf import PQMF
import dspgan.nhv.modules as modules

class ResStack(nn.Module):
    def __init__(self, channel, base=3, nums=4):
        super(ResStack, self).__init__()

        self.layers = nn.ModuleList([
            nn.Sequential(
                nn.LeakyReLU(),
                nn.Conv1d(channel, channel,
                    kernel_size=3, dilation=base**i, padding=base**i),
                nn.LeakyReLU(),
                nn.Conv1d(channel, channel,
                    kernel_size=3, dilation=1, padding=1),
            )
            for i in range(nums)
        ])
        self.resweight = nn.Parameter(torch.Tensor([0]))

    def forward(self, x):
        for layer in self.layers:
            x = x + self.resweight * layer(x)
        x = F.leaky_relu(x, 0.2)
        return x


class UpsampleNet(nn.Module):
    def __init__(self,
                 input_size,
                 output_size,
                 upsample_factor):

        super(UpsampleNet, self).__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.upsample_factor = upsample_factor

        layer = nn.ConvTranspose1d(input_size, output_size, upsample_factor * 2,
                                   upsample_factor, padding=upsample_factor // 2)
        self.layer = layer

    def forward(self, inputs):
        '''
        first_outputs = self.pixel_layer(inputs)
        first_outputs = first_outputs.unsqueeze(-1)
        first_outputs = self.pixel_shuffle(first_outputs)
        first_outputs= first_outputs.view(first_outputs.size(0), self.output_size, -1)
        '''
        second_outputs = self.layer(inputs)
        second_outputs = second_outputs[:, :, : inputs.size(-1) * self.upsample_factor]

        return second_outputs

class Melgan(nn.Module):
        #self.postfilter_fn.requires_grad=False
    def __init__(self, **kwargs):
        super().__init__()
        self.process_convs = nn.Conv1d(80, 512, kernel_size=7, padding=3)
        resblock_kernel_sizes = [3, 7, 11]
        resblock_dilation_sizes = [[1,3,5],[1,3,5],[1,3,5]]

        upsample_rates = [10,5,4,3]
        upsample_initial_channel = 512
        upsample_kernel_sizes = [20, 10, 8, 6]

        downsample_rates = [3,4,5,10]
        down_initial_channel = 32
        downsample_kernel_sizes = [6, 8, 10, 20]
        self.conv_se = nn.Conv1d(1, down_initial_channel, kernel_size=3, padding=1)
        self.upsample_rates = upsample_rates
        self.downsample_rates = downsample_rates
        self.num_kernels = len(resblock_kernel_sizes)
        self.num_upsamples = len(upsample_rates)
        resblock = modules.ResBlock1

        self.ups = nn.ModuleList()
        for i, (u, k) in enumerate(zip(upsample_rates, upsample_kernel_sizes)):
            self.ups.append(ConvTranspose1d(upsample_initial_channel // (2 ** i), upsample_initial_channel// (2 ** (i+1)), k, u, padding=(k-u)//2))

        self.resblock_up = nn.ModuleList()
        for i in range(len(self.ups)):
            ch = upsample_initial_channel // (2 ** (i+1))
            for j, (k, d) in enumerate(zip(resblock_kernel_sizes, resblock_dilation_sizes)):
                self.resblock_up.append(resblock(ch, k, d))

        self.downs = nn.ModuleList()
        #self.downs.append(nn.Conv1d(2, down_initial_channel, 3, padding=1))
        for i, (u, k) in enumerate(zip(downsample_rates, downsample_kernel_sizes)):

            self.downs.append(nn.Conv1d(down_initial_channel * (2 ** i), down_initial_channel * (2 ** (i+1)), k, u, padding=u))
        #(self.downs)
        self.resblock_down = nn.ModuleList()
        for i in range(len(self.downs)):
            ch = down_initial_channel * (2 ** (i+1))
            self.resblock_down.append(resblock(ch, 7, [1,3,5]))
            #for j, (k, d) in enumerate(zip(resblock_kernel_sizes, resblock_dilation_sizes)):
            #    self.resblock_down.append(resblock(ch, k, d))

        self.conv_post = nn.Conv1d(down_initial_channel, 1, kernel_size=7, padding=3)

        self.concat_pre = nn.ModuleList()
        for i in range(len(self.ups)):
            ch = upsample_initial_channel // (2 ** (i+1))
            self.concat_pre.append(nn.Conv1d(ch * 2, ch, kernel_size=3, padding=1))
        '''
        self.upsample_rates = upsample_rates
        self.num_kernels = len(resblock_kernel_sizes)
        self.num_upsamples = len(upsample_rates)
        resblock = modules.ResBlock1

        self.ups = nn.ModuleList()
        for i, (u, k) in enumerate(zip(upsample_rates, upsample_kernel_sizes)):
            self.ups.append(ConvTranspose1d(upsample_initial_channel // (2 ** i), upsample_initial_channel// (2 ** (i+1)), k, u, padding=(k-u)//2))

        self.resblock_up = nn.ModuleList()
        for i in range(len(self.ups)):
            ch = upsample_initial_channel // (2 ** (i+1))
            for j, (k, d) in enumerate(zip(resblock_kernel_sizes, resblock_dilation_sizes)):
                self.resblock_up.append(resblock(ch, k, d))

        self.conv_post = nn.Conv1d(ch, 1, kernel_size=7, padding=3)
        '''

    def forward(self, audio_6k, harmonic_noise, x, pitch):
        #x = x.transpose(1, 2)
        #unvoice = origin.detach() * (torch.ones_like(uv) - uv)
        #uplusv = voice + unvoice
        #y_6k = harmonic_noise[:, 1, :].unsqueeze(1).detach()

        harmonic = pitch.detach()
        nhv_mel = audio_6k.detach()
        y_6k = harmonic
        y_6k = self.conv_se(y_6k)
        y = [y_6k]
        for i in range(self.num_upsamples):
            in_size = y_6k.size(2)
            y_6k = F.leaky_relu(y_6k, 0.1)
            y_6k = self.downs[i](y_6k)
            y_6k = y_6k[:, :, : in_size // self.downsample_rates[i]]
            #print("y: ", y_6k.shape)
            ys = self.resblock_down[i](y_6k)
            y_6k = ys
            y.append(y_6k)

        x = nhv_mel
        x = self.process_convs(x)
        for i in range(self.num_upsamples):
            #print("x: ", x.shape)
            in_size = x.size(2)
            x = F.leaky_relu(x, 0.1)

            x = self.ups[i](x)
            x = x[:, :, : in_size * self.upsample_rates[i]]
            x = torch.cat([x, y[self.num_upsamples - i - 1]], 1)
            x = self.concat_pre[i](x)
            xs = None
            for j in range(self.num_kernels):
                if xs is None:
                    xs = self.resblock_up[i * self.num_kernels + j](x)
                else:
                    xs += self.resblock_up[i * self.num_kernels + j](x)
            x = xs / self.num_kernels


        x = F.leaky_relu(x, 0.1)
        x = self.conv_post(x)
        x = F.tanh(x)

        return x
        '''
        x = self.process_convs(x)
        for i in range(self.num_upsamples):
            #in_size = x.size(2)
            x = F.leaky_relu(x, 0.1)
            x = self.ups[i](x)
            #print("x: ", x.shape)
            #x = x[:, :, : in_size * self.upsample_rates[i]]
            #print("x_new: ", x.shape)
            xs = None
            for j in range(self.num_kernels):
                if xs is None:
                    xs = self.resblock_up[i * self.num_kernels + j](x)
                else:
                    xs += self.resblock_up[i * self.num_kernels + j](x)
            x = xs / self.num_kernels


        x = F.leaky_relu(x, 0.1)
        x = self.conv_post(x)
        x = F.tanh(x)

        return x
        '''

class NeuralHomomorphicVocoder(nn.Module):
    fs = 16000
    fft_size = 1024
    hop_size = 200
    in_channels = 80
    conv_channels = 256
    ccep_size = 222
    out_channels = 1
    kernel_size = 3
    dilation_size = 1
    group_size = 8
    fmin = 0
    fmax = 8000
    roll_size = 24
    n_ltv_layers = 3
    n_postfilter_layers = 4
    n_ltv_postfilter_layers = 1
    harmonic_amp = 0.1
    noise_std = 0.03
    use_causal = False
    use_reference_mag = False
    use_tanh = False
    use_uvmask = True
    use_weight_norm = True
    conv_type = "original"
    postfilter_type = None
    ltv_postfilter_type = None
    ltv_postfilter_kernel_size = 128
    scaler_file = None

    def __init__(self, **kwargs):
        super().__init__()
        for k, v in kwargs.items():
            if k not in self.__class__.__dict__.keys():
                raise ValueError(f"{k} not in arguments {self.__class__}.")
            setattr(self, k, v)
        # load scaler
        self.feat_scaler_fn = self._load_feat_scaler(self.scaler_file, ext="mlfb")

        # feat to linear spectrogram if use_reference_mag
        # self.feat2linear_fn = self._get_feat2linear_fn(ext="mlfb")

        # impulse generator
        self.impulse_generator = SinusoidsGenerator(
            hop_size=self.hop_size,
            fs=self.fs,
            harmonic_amp=self.harmonic_amp,
            use_uvmask=self.use_uvmask,
        )

        # LTV modules
        self.ltv_params = self._get_ltv_params()
        self.ltv_harmonic = CCepLTVFilter(
            **self.ltv_params, feat2linear_fn=None
        )
        self.ltv_noise = CCepLTVFilter(**self.ltv_params)

        # post filter
        self.postfilter_fn = self._get_postfilter_fn()
        self.conv = ConvLayers(in_channels=self.in_channels, conv_channels=self.conv_channels, out_channels=self.in_channels, kernel_size=self.kernel_size, dilation_size=self.dilation_size, n_conv_layers=self.n_ltv_layers, use_causal=self.use_causal, conv_type=self.conv_type)
        self.estimator = ConvLayers(in_channels=self.in_channels, conv_channels=self.conv_channels, out_channels=self.in_channels, kernel_size=self.kernel_size, dilation_size=self.dilation_size, n_conv_layers=self.n_ltv_layers, use_causal=self.use_causal, conv_type=self.conv_type)


        if self.use_weight_norm:
            self._apply_weight_norm()

    def _forward(self, z, x, cf0, uv):
        """
        z: (B, 1, T * hop_size)
        x: (B, T, D)
        cf0: (B, T, 1)
        uv: (B, T, 1)
        """
        if self.feat_scaler_fn is not None:
            x = self.feat_scaler_fn(x)
        x_conv = self.conv(x)
        a = self.estimator(x)
        x_harm = a * x_conv
        x_noise = (1 - a) * x_conv

        harmonic, harmonic_pitch = self.impulse_generator(x, cf0, uv)
        sig_harm = self.ltv_harmonic(x_harm, harmonic)
        sig_noise = self.ltv_noise(x_noise, z)
        y = sig_harm + sig_noise

        if self.postfilter_fn is not None:
            y = self.postfilter_fn(y.transpose(1, 2)).transpose(1, 2)
        y = torch.tanh(y) if self.use_tanh else torch.clamp(y, -1, 1)
        return y.reshape(x.size(0), self.out_channels, -1), y, harmonic_pitch

    def forward(self, z, c):
        """Interface for PWG trainer
        z: (B, T, D)
        c: (B, T, n_mels + 2)
        """
        c = c.transpose(1, 2)
        x, cf0, uv = torch.split(c, [self.in_channels, 1, 1], dim=-1)
        # NOTE: override z by Gaussian noise with arbitrary std
        z = torch.normal(0, self.noise_std, z.size()).to(z.device)
        y, o, harmonic_pitch = self._forward(z, x, cf0, uv)
        return y, o, harmonic_pitch

    @torch.no_grad()
    def inference(self, c):
        """Interface for PWG decoder
        c: (T, D)
        """
        c = c.unsqueeze(0)
        z = torch.normal(0, self.noise_std, (1, c.size(1) * self.hop_size)).to(c.device)
        x, cf0, uv = torch.split(c, [self.in_channels, 1, 1], dim=-1)
        y = self._forward(z, x, cf0, uv)
        return y.squeeze(0)

    def remove_weight_norm(self):
        def _remove_weight_norm(m):
            try:
                torch.nn.utils.remove_weight_norm(m)
            except ValueError:
                return

        self.apply(_remove_weight_norm)

    def _apply_weight_norm(self):
        def _apply_weight_norm(m):
            if isinstance(m, torch.nn.Conv1d):
                torch.nn.utils.weight_norm(m)

        self.apply(_apply_weight_norm)

    def _get_ltv_params(self):
        return {
            "in_channels": self.in_channels,
            "conv_channels": self.conv_channels,
            "ccep_size": self.ccep_size,
            "kernel_size": self.kernel_size,
            "dilation_size": self.dilation_size,
            "group_size": self.group_size,
            "fft_size": self.fft_size,
            "hop_size": self.hop_size,
            "n_ltv_layers": self.n_ltv_layers,
            "n_ltv_postfilter_layers": self.n_ltv_postfilter_layers,
            "use_causal": self.use_causal,
            "conv_type": self.conv_type,
            "ltv_postfilter_type": self.ltv_postfilter_type,
            "ltv_postfilter_kernel_size": self.ltv_postfilter_kernel_size,
        }

    @staticmethod
    def _load_feat_scaler(scaler_file, ext="mlfb"):
        if scaler_file is not None:
            if ext == "mlfb":
                fn = LogMelSpectrogramScaler(joblib.load(scaler_file)[ext])
            elif ext == "lsp":
                fn = None
                raise NotImplementedError("lsp scaler is not implemented.")
        else:
            fn = None
        return fn

    def _get_feat2linear_fn(self, ext="mlfb"):
        if self.use_reference_mag:
            if ext == "mlfb":
                fn = LogMelSpectrogram2LogMagnitude(
                    fs=self.fs,
                    fft_size=self.fft_size,
                    n_mels=self.in_channels,
                    fmin=self.fmin,
                    fmax=self.fmax,
                    roll_size=self.roll_size,
                    melspc_scaler_fn=self.feat_scaler_fn,
                )
            elif ext == "lsp":
                fn = None
                raise NotImplementedError("lsp to linear is not implemented.")
        else:
            fn = None
        return fn

    def _get_postfilter_fn(self):
        if self.postfilter_type == "ddsconv":
            fn = ConvLayers(
                in_channels=1,
                conv_channels=64,
                out_channels=1,
                kernel_size=5,
                dilation_size=2,
                n_conv_layers=self.n_postfilter_layers,
                use_causal=self.use_causal,
                conv_type="ddsconv",
            )
        elif self.postfilter_type == "conv":
            fn = Conv1d(
                in_channels=1,
                out_channels=1,
                kernel_size=self.fft_size,
                use_causal=self.use_causal,
            )
        elif self.postfilter_type is None:
            fn = None
        else:
            raise ValueError(f"Invalid postfilter_type: {self.postfilter_type}")
        return fn
