import torch
from torch.nn import functional as F
from torch import nn
import commons
import math

def feature_loss(fmap_r, fmap_g):
  loss = 0
  for key, fmap_r_tmp in fmap_r.items():
    for dr, dg in zip(fmap_r_tmp, fmap_g[key]):
      for rl, gl in zip(dr, dg):
        rl = rl.float().detach()
        gl = gl.float()
        loss += torch.mean(torch.abs(rl - gl))
  return loss


def discriminator_loss(disc_real_outputs, disc_generated_outputs):
  loss = 0
  r_losses = []
  g_losses = []
  for key, disc_real_outputs_tmp in disc_real_outputs.items():
    for dr, dg in zip(disc_real_outputs_tmp, disc_generated_outputs[key]):
      dr = dr.float()
      dg = dg.float()
      r_loss = torch.mean((1-dr)**2)
      g_loss = torch.mean(dg**2)
      loss += (r_loss + g_loss)
      r_losses.append(r_loss.item())
      g_losses.append(g_loss.item())
  loss = loss / 2

  return loss, r_losses, g_losses


def generator_loss(disc_outputs):
  loss = 0
  gen_losses = []
  for key, disc_outputs_tmp in disc_outputs.items():
    for dg in disc_outputs_tmp:
      dg = dg.float()
      l = torch.mean((1-dg)**2)
      gen_losses.append(l)
      loss += l
  loss = loss / 2
  return loss, gen_losses


def kl_loss(z_p, logs_q, m_p, logs_p, z_mask):
  """
  z_p, logs_q: [b, h, t_t]
  m_p, logs_p: [b, h, t_t]
  """
  z_p = z_p.float()
  logs_q = logs_q.float()
  m_p = m_p.float()
  logs_p = logs_p.float()
  z_mask = z_mask.float()

  kl = logs_p - logs_q - 0.5
  kl += 0.5 * ((z_p - m_p)**2) * torch.exp(-2. * logs_p)
  kl = torch.sum(kl * z_mask)
  l = kl / torch.sum(z_mask)
  return l

def kl_losstmp(z, m, logs, logdet, mask):
  l = torch.sum(logs) + 0.5 * torch.sum(torch.exp(-2 * logs) * ((z - m)**2))
  l = l - torch.sum(logdet)
  l = l / torch.sum(torch.ones_like(z) * mask)
  l = l + 0.5 * math.log(2 * math.pi)
  return l

def loglikehood(z, m, logs, z_mask):
  """
  z_p, logs_q: [b, h, t_t]
  m_p, logs_p: [b, h, t_t]
  """
  z = z.float()
  m = m.float()
  logs = logs.float()
  z_mask = z_mask.float()

  # z = torch.detach(z)
  std = torch.exp(logs)
  mean = m
  factors = 1 / math.sqrt(2 * math.pi) / std * z_mask
  exponent = torch.exp(-1 / 2 * torch.square((z - mean) / std)) * z_mask

  # 混合高斯模型下的似然函数(取值为y)
  GMM_likelihood = factors * exponent * z_mask

  # 加负号: 梯度下降 ==> 似然函数最大
  log_likelihood = - torch.log(torch.clamp(GMM_likelihood, min=1e-8)) * z_mask
  log_likelihood = torch.mean(log_likelihood)
  return log_likelihood

# class MultiResolutionSTFTLoss(nn.Module):
#     def __init__(self,
#                  fft_sizes=[128*2, 256*2, 384*2, 512*2, 640*2, 768*2, 896*2, 1024*2, 1536*2, 2048*2, 3072*2, 4096*2],
#                  win_sizes=[128, 256, 384, 512, 640, 768, 896, 1024, 1536, 2048, 3072, 4096],
#                  hop_sizes=[128//2, 256//2, 384//2, 512//2, 640//2, 768//2, 896//2, 1024//2, 1536//2, 2048//2, 3072//2, 4096//2]):
class MultiResolutionSTFTLoss(nn.Module):
    def __init__(self,
                 fft_sizes=[128*2, 256*2, 384*2, 512*2, 1024*2],
                 win_sizes=[128, 256, 384, 512, 1024],
                 hop_sizes=[128//2, 256//2, 384//2, 512//2, 1024//2]):
        super(MultiResolutionSTFTLoss, self).__init__()

        self.loss_layers = torch.nn.ModuleList()
        for (fft_size, win_size, hop_size) in zip(fft_sizes, win_sizes, hop_sizes):
            self.loss_layers.append(STFTLoss(fft_size, hop_size, win_size))

    def forward(self, fake_signals, true_signals):
        fake_signals = fake_signals.squeeze(1)
        true_signals = true_signals.squeeze(1)
        sc_losses = []
        mag_losses = []
        for layer in self.loss_layers:
            sc_loss, mag_loss = layer(fake_signals, true_signals)
            sc_losses.append(sc_loss)
            mag_losses.append(mag_loss)

        sc_loss = sum(sc_losses) / len(sc_losses)
        mag_loss = sum(mag_losses) / len(mag_losses)

        return sc_loss, mag_loss

class STFTLoss(nn.Module):
    def __init__(self,
                 fft_size,
                 hop_size,
                 win_size):
        super(STFTLoss, self).__init__()

        self.fft_size = fft_size
        self.hop_size = hop_size
        self.win_size = win_size
        self.window = torch.hann_window(win_size)
        self.sc_loss = SpectralConvergence()
        self.mag_loss = LogSTFTMagnitude()

    def forward(self, predicts, targets):
        predicts_mag = stft(predicts, self.fft_size, self.hop_size, self.win_size, self.window)
        targets_mag = stft(targets, self.fft_size, self.hop_size, self.win_size, self.window)
        sc_loss = self.sc_loss(predicts_mag, targets_mag)
        mag_loss = self.mag_loss(predicts_mag, targets_mag)
        return sc_loss, mag_loss

class SpectralConvergence(nn.Module):
    def __init__(self):
        """Initilize spectral convergence loss module."""
        super(SpectralConvergence, self).__init__()

    def forward(self, predicts_mag, targets_mag):
        # x = torch.norm(targets_mag - predicts_mag, p='fro')
        # y = torch.norm(targets_mag, p='fro')
        outputs = F.l1_loss(predicts_mag, targets_mag)

        return outputs

class LogSTFTMagnitude(nn.Module):
    def __init__(self):
        super(LogSTFTMagnitude, self).__init__()

    def forward(self, predicts_mag, targets_mag):
        log_predicts_mag = torch.log(predicts_mag)
        log_targets_mag = torch.log(targets_mag)

        outputs = F.l1_loss(log_predicts_mag, log_targets_mag)

        return outputs

def stft(x, fft_size, hop_size, win_size, window):
    """Perform STFT and convert to magnitude spectrogram.

    Args:
        x: Input signal tensor (B, T).

    Returns:
        Tensor: Magnitude spectrogram (B, T, fft_size // 2 + 1).

    """
    window = window.to(x.device)
    x_stft = torch.stft(x, fft_size, hop_size, win_size, window)
    real = x_stft[..., 0]
    imag = x_stft[..., 1]
    outputs = torch.clamp(real ** 2 + imag ** 2, min=1e-7).transpose(2, 1)
    outputs = torch.sqrt(outputs)

    return outputs