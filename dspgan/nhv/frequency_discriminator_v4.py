# Copyright 2021 ASLP@NPU.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# Author: <EMAIL> (songkun)


import torch
import torch.nn as nn
from typing import Optional
from torch import Tensor
import math


def create_fb_matrix(n_freqs: int,
                     f_min: float,
                     f_max: float,
                     n_mels: int,
                     sample_rate: int,
                     norm: Optional[str] = None) -> Tensor:
    """Create a frequency bin conversion matrix.

    Args:
        n_freqs (int): Number of frequencies to highlight/apply
        f_min (float): Minimum frequency (Hz)
        f_max (float): Maximum frequency (Hz)
        n_mels (int): Number of mel filterbanks
        sample_rate (int): Sample rate of the audio waveform
        norm (Optional[str]): If 'slaney', divide the triangular mel weights by the width of the mel band
        (area normalization). (Default: ``None``)

    Returns:
        Tensor: Triangular filter banks (fb matrix) of size (``n_freqs``, ``n_mels``)
        meaning number of frequencies to highlight/apply to x the number of filterbanks.
        Each column is a filterbank so that assuming there is a matrix A of
        size (..., ``n_freqs``), the applied result would be
        ``A * create_fb_matrix(A.size(-1), ...)``.
    """

    if norm is not None and norm != "slaney":
        raise ValueError("norm must be one of None or 'slaney'")

    # freq bins
    # Equivalent filterbank construction by Librosa
    all_freqs = torch.linspace(0, sample_rate // 2, n_freqs)

    # calculate mel freq bins
    # hertz to mel(f) is 2595. * math.log10(1. + (f / 700.))
    m_min = 2595.0 * math.log10(1.0 + (f_min / 700.0))
    m_max = 2595.0 * math.log10(1.0 + (f_max / 700.0))
    m_pts = torch.linspace(m_min, m_max, n_mels + 2)
    # mel to hertz(mel) is 700. * (10**(mel / 2595.) - 1.)
    f_pts = 700.0 * (10**(m_pts / 2595.0) - 1.0)
    # calculate the difference between each mel point and each stft freq point in hertz
    f_diff = f_pts[1:] - f_pts[:-1]  # (n_mels + 1)
    slopes = f_pts.unsqueeze(0) - all_freqs.unsqueeze(
        1)  # (n_freqs, n_mels + 2)
    # create overlapping triangles
    down_slopes = (-1.0 * slopes[:, :-2]) / f_diff[:-1]  # (n_freqs, n_mels)
    up_slopes = slopes[:, 2:] / f_diff[1:]  # (n_freqs, n_mels)
    fb = torch.min(down_slopes, up_slopes)
    fb = torch.clamp(fb, 1e-6, 1)

    if norm is not None and norm == "slaney":
        # Slaney-style mel is scaled to be approx constant energy per channel
        enorm = 2.0 / (f_pts[2:n_mels + 2] - f_pts[:n_mels])
        fb *= enorm.unsqueeze(0)
    return fb


class MelScale(torch.nn.Module):
    r"""Turn a normal STFT into a mel frequency STFT, using a conversion
    matrix.  This uses triangular filter banks.

    User can control which device the filter bank (`fb`) is (e.g. fb.to(spec_f.device)).

    Args:
        n_mels (int, optional): Number of mel filterbanks. (Default: ``128``)
        sample_rate (int, optional): Sample rate of audio signal. (Default: ``16000``)
        f_min (float, optional): Minimum frequency. (Default: ``0.``)
        f_max (float or None, optional): Maximum frequency. (Default: ``sample_rate // 2``)
        n_stft (int, optional): Number of bins in STFT. Calculated from first input
            if None is given.  See ``n_fft`` in :class:`Spectrogram`. (Default: ``None``)
    """
    __constants__ = ['n_mels', 'sample_rate', 'f_min', 'f_max']

    def __init__(self,
                 n_mels: int = 128,
                 sample_rate: int = 24000,
                 f_min: float = 0.,
                 f_max: Optional[float] = None,
                 n_stft: Optional[int] = None) -> None:
        super(MelScale, self).__init__()
        self.n_mels = n_mels
        self.sample_rate = sample_rate
        self.f_max = f_max if f_max is not None else float(sample_rate // 2)
        self.f_min = f_min

        assert f_min <= self.f_max, 'Require f_min: %f < f_max: %f' % (
            f_min, self.f_max)

        fb = torch.empty(0) if n_stft is None else create_fb_matrix(
            n_stft, self.f_min, self.f_max, self.n_mels, self.sample_rate)
        self.register_buffer('fb', fb)

    def forward(self, specgram: Tensor) -> Tensor:
        r"""
        Args:
            specgram (Tensor): A spectrogram STFT of dimension (..., freq, time).

        Returns:
            Tensor: Mel frequency spectrogram of size (..., ``n_mels``, time).
        """

        # pack batch
        shape = specgram.size()
        specgram = specgram.reshape(-1, shape[-2], shape[-1]).transpose(1, 2)

        # (channel, frequency, time).transpose(...) dot (frequency, n_mels)
        # -> (channel, time, n_mels).transpose(...)
        mel_specgram = torch.matmul(specgram, self.fb.clone())
        mel_specgram = mel_specgram.transpose(1, 2)
        # unpack batch
        mel_specgram = mel_specgram.reshape(shape[:-2] +
                                            mel_specgram.shape[-2:]).clone()

        return mel_specgram


class TorchSTFT(torch.nn.Module):

    def __init__(self,
                 fft_size,
                 hop_size,
                 win_size,
                 normalized=False,
                 domain='linear',
                 mel_scale=False,
                 ref_level_db=20,
                 min_level_db=-100):
        super().__init__()
        self.fft_size = fft_size
        self.hop_size = hop_size
        self.win_size = win_size
        self.ref_level_db = ref_level_db
        self.min_level_db = min_level_db
        self.window = torch.hann_window(win_size)
        self.normalized = normalized
        self.domain = domain
        self.mel_scale = MelScale(n_mels=(fft_size // 2 + 1),
                                  n_stft=(fft_size // 2 +
                                          1)) if mel_scale else None

    def transform(self, x):
        x_stft = torch.stft(x,
                            self.fft_size,
                            self.hop_size,
                            self.win_size,
                            self.window.type_as(x),
                            normalized=self.normalized)
        real = x_stft[..., 0]
        imag = x_stft[..., 1]
        mag = torch.clamp(real**2 + imag**2, min=1e-7)
        mag = torch.sqrt(mag)
        phase = torch.atan2(imag, real)

        if self.mel_scale is not None:
            mag = self.mel_scale(mag)

        if self.domain == 'log':
            mag = 20 * torch.log10(mag) - self.ref_level_db
            mag = torch.clamp((mag - self.min_level_db) / -self.min_level_db, 0,
                              1)
            return mag, phase
        elif self.domain == 'linear':
            return mag, phase
        elif self.domain == 'double':
            log_mag = 20 * torch.log10(mag) - self.ref_level_db
            log_mag = torch.clamp(
                (log_mag - self.min_level_db) / -self.min_level_db, 0, 1)
            return torch.cat((mag, log_mag), dim=1), phase

    def complex(self, x):
        x_stft = torch.stft(x,
                            self.fft_size,
                            self.hop_size,
                            self.win_size,
                            self.window.type_as(x),
                            normalized=self.normalized)
        real = x_stft[..., 0]
        imag = x_stft[..., 1]
        return real, imag


class Discriminator(nn.Module):

    def __init__(self, in_channels, hidden_channels=512):
        super(Discriminator, self).__init__()

        self.discriminator = nn.ModuleList()
        self.discriminator += [
            nn.Sequential(
                nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(in_channels,
                              hidden_channels // 32,
                              kernel_size=(3, 3),
                              stride=(1, 1)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels // 32,
                              hidden_channels // 16,
                              kernel_size=(3, 3),
                              stride=(2, 2)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels // 16,
                              hidden_channels // 8,
                              kernel_size=(3, 3),
                              stride=(1, 1)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels // 8,
                              hidden_channels // 4,
                              kernel_size=(3, 3),
                              stride=(2, 2)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels // 4,
                              hidden_channels // 2,
                              kernel_size=(3, 3),
                              stride=(1, 1)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels // 2,
                              hidden_channels,
                              kernel_size=(3, 3),
                              stride=(2, 2)))),
            nn.Sequential(
                nn.LeakyReLU(0.2, True), nn.ReflectionPad2d((1, 1, 1, 1)),
                nn.utils.weight_norm(
                    nn.Conv2d(hidden_channels,
                              1,
                              kernel_size=(3, 3),
                              stride=(1, 1))))
        ]

    def forward(self, x):
        hiddens = []
        for layer in self.discriminator:
            #rint("x: ", x.shape)
            x = layer(x)
            hiddens.append(x)
        return x, hiddens[:-1]


class FrequencyDiscriminatorV4(nn.Module):
    # Multi-scale FrequencyDiscriminator.
    # author: chenyi
    def __init__(self, domain='double', hop_lengths=[15, 30, 60, 120, 240, 480], hidden_channels=[128, 128, 256, 256, 512, 512], mel_scale=True):
        super(FrequencyDiscriminatorV4, self).__init__()
        #hop_lengths = [15, 30, 60, 120, 240, 480]
        #hidden_channels = [128, 128, 256, 256, 512, 512]

        self.stfts = nn.ModuleList([
            TorchSTFT(fft_size=x * 4,
                      hop_size=x,
                      win_size=x * 4,
                      normalized=True,
                      domain=domain,
                      mel_scale=mel_scale) for x in hop_lengths
        ])
        self.domain = domain
        if domain == 'double':
            self.discriminators = nn.ModuleList([
                Discriminator(2, c)
                for x, c in zip(hop_lengths, hidden_channels)
            ])
        else:
            self.full_discriminators = nn.ModuleList([
                Discriminator(1, c)
                for x, c in zip(hop_lengths, hidden_channels)
            ])
            self.subband_1_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_2_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_3_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_4_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_5_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_6_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_7_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])
            self.subband_8_discriminators = nn.ModuleList([
                Discriminator(1, c // 8)
                for x, c in zip(hop_lengths, hidden_channels)])

    def forward(self, x):
        scores, feats = list(), list()
        for index in range(len(self.stfts)):
            stft = self.stfts[index]
            if self.domain == 'double':
                layer = self.discriminators[index]
            else:

                layer = self.full_discriminators[index]
                layer_1 = self.subband_1_discriminators[index]
                layer_2 = self.subband_2_discriminators[index]
                layer_3 = self.subband_3_discriminators[index]
                layer_4 = self.subband_4_discriminators[index]
                layer_5 = self.subband_5_discriminators[index]
                layer_6 = self.subband_6_discriminators[index]
                layer_7 = self.subband_7_discriminators[index]
                layer_8 = self.subband_8_discriminators[index]
            mag, phase = stft.transform(x.squeeze(1))
            if self.domain == 'double':
                mag = torch.stack(torch.chunk(mag, 2, dim=1), dim=1)
            else:
                mag = mag.unsqueeze(1)

            score, feat = layer(mag)
            if self.domain == 'double':
                scores.extend(score)
            #feats.append(feat)
                feats.extend(feat)
            else:
                scores.append(score)
                feats.append(feat)
                channel_subband = mag.shape[2] // 8
                score, feat = layer_1(mag[:, :, :channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_2(mag[:, :, channel_subband: 2 * channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_3(mag[:, :, 2 * channel_subband: 3 * channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_4(mag[:, :, 3 * channel_subband: 4 * channel_subband, :])
                scores.append(score)
                feats.append(feat) 
                
                score, feat = layer_1(mag[:, :, 4 * channel_subband: 5 * channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_2(mag[:, :, 5 * channel_subband: 6 * channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_3(mag[:, :, 6 * channel_subband: 7 * channel_subband, :])
                scores.append(score)
                feats.append(feat)

                score, feat = layer_4(mag[:, :, 7 * channel_subband:, :])
                scores.append(score)
                feats.append(feat) 
        return scores, feats


if __name__ == "__main__":
    model = FrequencyDiscriminatorV4()
    x = torch.randn(2, 1, 24000)
    scores, feats = model(x)
    print(len(scores), len(feats))
    for s, f in zip(scores, feats):
        print(len(s))
        print(len(f))
        print(s.shape)
        print(f.shape)
