import os,sys
import time
import argparse
import numpy as np
from scipy.io.wavfile import write,read


from singlecodec_dec import decode
from singlecodec_enc import encode
from dspgan.mel2lf0.gen_pitch2 import pitch_synthesis
from dspgan.nhv.resyn2 import generate_audio

out_mel_path = 0

def pitch_resyn(mel_path,use_cuda=True):
    return pitch_synthesis(mel_path=mel_path,use_cuda=use_cuda)

def audio_resyn(mel_path, pitch_path,use_cuda=True):
    return generate_audio(
        mel_path=mel_path,
        pitch_path=pitch_path,
        use_cuda=use_cuda
        )

def audio_codec(audio_path,use_cuda):
    codes, codes_s, size = encode(audio_path)
    mel = decode(out_mel_path, codes, codes_s)
    pitch = pitch_resyn(mel,use_cuda)
    audio = audio_resyn(mel, pitch,use_cuda)
    # print('audio codec done')
    return  size, audio


def audio_io(args):
    audio_path = args.i
    out_path = args.o
    use_cuda = args.use_cuda
    fs, audio = read(audio_path)
    _,a = audio_codec(audio_path,use_cuda)
    print('warm up done')
    wavlen = len(audio)/fs
    for i in range(100):
        s = time.time()
        _,a = audio_codec(audio_path,use_cuda)
        e = time.time()
        print('codec test:',i+1)
        print(f'audio len: {wavlen:.1f}s',f' codec time: {e-s:.3f}s')    
    write(out_path,48000,a)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('-i', type=str, default='test.wav', help='path of input audio')
    parser.add_argument('-o', type=str, default='output.wav', help='path of output audio')
    parser.add_argument('--use_cuda', type=bool, default=False, help='use cuda')
    args = parser.parse_args()
    audio_io(args)

