

**接口名称:**  `/api/v1/receive_audio`

**请求方式:**  `POST`

**请求参数:**

*   **Content-Type:**  `multipart/form-data` (因为包含文件上传)
*   **Form Data 参数:**
    *   `id`:  (必须)  字符串，通话的唯一标识符。
    *   `audio_file`: (必须)  文件，要处理的音频文件。

**成功响应 (状态码 200):**

*   **Content-Type:** `application/json`
*   **响应体 (JSON):**

```json
{
    "text": [ ... ],     // 编码后的结果 (整数列表)
    "text_s": [ ... ],    // 编码后的结果 (整数列表)
    "size":  ...,       // 原始音频帧数 (整数)
    "id":    "...",       // 与请求中的 id 相同 (字符串)
    "timestamp": "..."    // 处理完成的时间戳，格式为 "YYYY-MM-DD HH:MM:SS" (字符串)
}
```

**错误响应:**

*   **缺少参数 (状态码 400):**

```json
{
    "error": "No audio file or call ID provided"
}
```

*   **音频文件过小或为空 (状态码 400):**

```json
{
  "error": "Audio file is too small or empty"
}
```

*   **编码过程出错 (状态码 500):**

```json
{
    "error": "解析出错,请重试。"  // 或者更具体的错误信息
}
```

*  **处理音频文件失败 (状态码 500):**
```json
{
    "error": "Failed to process audio file: ..." // 包含具体的错误原因
}
```



## 解码服务启动
```
cd /home/<USER>/codec/export_se
conda activate codec
python dec_server.py

```
## 解码服务接口
### **输入接口：/api/v1/receive_codes**
#### **基本信息**  
- **接口地址**：`/api/v1/receive_codes`  
- **请求方法**：`POST`  
- **请求数据类型**：`application/json`  
- **接口功能**：接收编码后的文本数据，触发音频生成流程。  


#### **请求参数（Body）**  
| 参数名   | 类型       | 是否必填 | 描述                     | 示例值                     |  
|----------|------------|----------|--------------------------|----------------------------|  
| id       | 字符串     | 是       | 调用唯一标识符（如会话ID） | `"123456"`                 |  
| text     | 数组       | 是       | 文本数据（数值列表）     | `[[1, 2, 3, 4]]`             |  
| text_s   | 数组       | 是       | 相关文本数据（数值列表） | `[[1]]`          |  
| size     | 数值/字符串 | 否       | 数据大小（可选）         | `1024`                     |  
| timestamp| 字符串/数值 | 否       | 时间戳（可选）           | `"1680000000"`              |  


#### **响应参数（JSON）**  
| 参数名 | 类型   | 描述                     | 示例值                  |  
|--------|--------|--------------------------|-------------------------|  
| code   | 整数   | 状态码（`0`表示成功）    | `0`                     |  
| data   | 对象   | 回传的请求数据（部分字段）| `{"id": "123456", "text": [[1,2,3,4]]` |  
| msg    | 字符串 | 状态描述                 | `"enc result received"` |  


#### **状态码**  
- `200`：请求处理成功，触发音频生成线程。  
- 其他状态码：未在代码中明确定义，默认遵循HTTP标准（如`400`请求格式错误）。  


### **输出接口：/api/v1/receive_decaudio**  
#### **基本信息**  
- **接口地址**：`/api/v1/receive_decaudio`  
- **请求方法**：`POST`  
- **请求数据类型**：`multipart/form-data`（包含文件和表单数据）  
- **接口功能**：接收生成的音频文件，完成解码后的处理（如存储或进一步验证）。  


#### **请求参数**  
| 参数名     | 类型       | 是否必填 | 描述                     | 示例值                     |  
|------------|------------|----------|--------------------------|----------------------------|  
| audio_file | 文件（WAV）| 是       | 生成的音频文件（48kHz采样率） | 二进制文件流               |  
| id         | 字符串     | 是       | 调用唯一标识符（与输入接口一致） | `"123456"`                 |  


#### **响应参数（JSON）**  
| 参数名 | 类型   | 描述                     | 示例值                  |  
|--------|--------|--------------------------|-------------------------|  
| message| 字符串 | 接收状态描述             | `"Audio received"`      |  


#### **状态码**  
- `200`：音频文件接收成功。  
- `400`：请求参数缺失（如无`audio_file`或`id`）或文件无效（如文件过小）。  
  - 错误示例：`{"error": "No audio file or call ID provided"}`  
- `500`：服务器内部错误（如文件处理失败）。  
  - 错误示例：`{"error": "Failed to process audio file: ..."}`  

