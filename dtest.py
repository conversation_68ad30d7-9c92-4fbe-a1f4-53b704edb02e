import requests
import time
import os
import datetime
import logging
import urllib3
import tempfile
import json
import time
import sys
import numpy as np
from flask import Flask, request, jsonify
app = Flask(__name__)
http = urllib3.PoolManager()
@app.route('/api/v1/receive_decaudio', methods=['POST'])
def receive_audio():
    data = request.form.to_dict()
    call_id = data.get('id')
    
    print("Received data:")
    print("ID:", call_id)
    rdec = 'rdec'
    if 'audio_file' not in request.files or not call_id:
        return jsonify({"error": "No audio file or call ID provided"}), 400

    # audio_file = request.files['audio_file']
    # try:
    #     storage_path = 'temp_audio/'
    #     _, file_extension = os.path.splitext(audio_file.filename)
    #     audio_filename = f"{call_id}{rdec}{file_extension}"
    #     audio_filepath = os.path.join(storage_path, audio_filename)
    #     audio_file.save(audio_filepath)
    #     if os.path.getsize(audio_filepath) < 100:
    #         os.unlink(audio_filepath)
    #         return jsonify({"error": "Audio file is too small or empty"}), 400

    # except Exception as e:
    #     return jsonify({"error": f"Failed to process audio file: {str(e)}"}), 500

    responsemsg = jsonify({"message": "Audio received"}), 200

    return responsemsg


if __name__ == '__main__':
    app.run(host='127.0.0.1', port=39006)

