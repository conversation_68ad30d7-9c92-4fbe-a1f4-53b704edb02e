### 快速开始

python版本：python==3.10

### 环境安装：

```bash
pip install -r requirements.txt
```

### 快速测试：

```bash
python demo_f.py
```

运行开始时会产生较多log，看到下面的信息表示编解码开始：

```bash
Trainable Parameters: 1.070 million
Restore from dspgan/mel2lf0/logdir/16k/model.ckpt-90000.pt
nhv params :  3.803522
load 
INFO:root:Loaded checkpoint 'dspgan/nhv/ckpts/G3k_600000.pth' (iteration 8)
load 
INFO:root:Loaded checkpoint 'dspgan/nhv/ckpts/model.pth' (iteration 22)
/root/miniconda3/envs/stest/lib/python3.10/site-packages/torch/functional.py:665: UserWarning: stft with return_complex=False is deprecated. In a future pytorch release, stft will return complex tensors for all inputs, and return_complex=False will raise an error.
Note: you can still call torch.view_as_real on the complex output to recover the old return format. (Triggered internally at ../aten/src/ATen/native/SpectralOps.cpp:873.)
  return _VF.stft(input, n_fft, hop_length, win_length, window,  # type: ignore[attr-defined]
warm up done
codec test: 1
audio len: 4.1s  codec time: 0.602s
...
```

### 参数说明：

可以自定义输入输出音频以及是否使用CUDA。

```python
"""
'-i', type=str, default='test.wav', 输入音频文件
'-o', type=str, default='output.wav', 输出音频文件
'--use_cuda', type=bool, default=True, 是否使用CUDA
"""
```


### 测试说明

etest (raw audio) -> enc_server (codes) -> dec_server (dec audio) -> dtest (receive dec audio)