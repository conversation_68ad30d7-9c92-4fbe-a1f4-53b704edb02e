import torch
import sys
import librosa
import numpy as np
import math
import random  

from extractors import process_wav, __extract_mel
from config import preConfiged16K

global_sm = None
export_path = 'refvq_v1_310000_export.pt'

def encode(input_path, device='cpu'):
    global global_sm
    if global_sm is None:
        global_sm = torch.jit.load(export_path, map_location=device)
        global_sm.eval()
        global_sm = global_sm.to(device)
    
    wav = process_wav(input_path, preConfiged16K)
    mel, _, _ = __extract_mel(wav, preConfiged16K)
    
    input = torch.from_numpy(mel).unsqueeze(0).to(device)
    input = input + 1
    input = input / 2

    if input.shape[2] >= 600:
        max_start_index = input.shape[2] - 600
        start_index = random.randint(0, max_start_index)

        promt = input[:,:,start_index:start_index+600]
    else:
        promt = input.tile((1, 1, math.ceil(1.0 * 600 / input.shape[2])))
        promt = promt[:, :, :600]
        
    with torch.no_grad():

        sampled, _, codes, sampled_s, _, codes_s = global_sm.encode(input.cpu(), promt.cpu())
        
    codes = codes.cpu().numpy()
    codes_s = codes_s.cpu().numpy()
    size = (codes.shape[1] + codes_s.shape[1])*13

    return codes, codes_s, size
