import datetime
import logging
import urllib3
import tempfile
import json
import time
import re
import sys
import numpy as np
import requests
from scipy.io.wavfile import write
from singlecodec_dec import decode
from flask import Flask, request, jsonify
from dspgan.mel2lf0.gen_pitch2 import pitch_synthesis
from dspgan.nhv.resyn2 import generate_audio
import torchaudio
import os

ims_app = Flask(__name__)
out_mel_path = 0
SUPPORTED_FORMATS = ['.mp3', '.m4a', '.aac', '.amr']
TARGET_FORMAT = '.mp3' 

def convert_audio(input_path, output_path, target_format):
    try:
        if target_format not in SUPPORTED_FORMATS:
            print(f"不支持的目标格式: {target_format}")
            return False
        waveform, sample_rate = torchaudio.load(input_path)
        torchaudio.save(output_path, waveform, sample_rate)
        return True
    except Exception as e:
        print(f"转码 {input_path} 失败: {e}")
        return False

@ims_app.route('/api/v1/receive_codes', methods=['POST'])
def receive_asr_result():
    data = request.json
    call_id = data.get('id')
    text = data.get('text')
    text_s = data.get('text_s')
    size = data.get('size')
    timestamp = data.get('timestamp')
    try:
        text_np = np.array(text)
        text_s_np = np.array(text_s)
    except Exception as e:
        print(f"Error converting lists to NumPy arrays: {e}")

    print("Received data:")
    print("ID:", call_id)
    print("Text:", text)
    print("Timestamp:", timestamp)

    # 同步生成音频，并获取文件大小
    audio_file_path, audio_file_size = audio_io(text_np, text_s_np, size, call_id)

    response_data = {
        "code": 0,
        "data": {
            "id": call_id,
            "text": text,
            "timestamp": timestamp,
            "audio_file_size": audio_file_size  # 新增字段
        },
        "msg": "enc result received"
    }
    print(audio_file_size)
    return jsonify(response_data), 200

def pitch_resyn(mel_path, use_cuda=False):
    return pitch_synthesis(mel_path=mel_path, use_cuda=use_cuda)

def audio_resyn(mel_path, pitch_path, use_cuda=False):
    return generate_audio(
        mel_path=mel_path,
        pitch_path=pitch_path,
        use_cuda=use_cuda
    )

def audio_codec(text_np, text_s_np, use_cuda):
    mel = decode(out_mel_path, text_np, text_s_np)
    pitch = pitch_resyn(mel, use_cuda)
    audio = audio_resyn(mel, pitch, use_cuda)
    return audio

def audio_io(text_np, text_s_np, size, call_id):
    wav_out_path = f'temp_audio/{call_id}_out.wav'
    final_out_path = f'temp_audio/{call_id}_out{TARGET_FORMAT}' 
    use_cuda = False

    s = time.time()
    a = audio_codec(text_np, text_s_np, use_cuda)
    e = time.time()
    print(f' codec time: {e - s:.3f}s', ' bit:', size)

    write(wav_out_path, 48000, a)
    print('audio_io finished, saved to:', wav_out_path)

    if wav_out_path.lower().endswith('.wav'):
        convert_audio(wav_out_path, final_out_path, TARGET_FORMAT)
        os.remove(wav_out_path)

    # 获取转换后的文件大小
    if os.path.exists(final_out_path):
        audio_file_size = os.path.getsize(final_out_path)
    else:
        audio_file_size = 0

    return final_out_path, audio_file_size

if __name__ == '__main__':
    if not os.path.exists('temp_audio'):
        os.makedirs('temp_audio')
    t_np = np.array([[1992, 3255, 3255, 6898, 1132, 4043, 3148, 5235, 
    5060, 1828, 2749, 5951, 2420, 7177, 4180, 2622, 4746, 3855, 2269, 4749]])
    t_s_np = np.array([[605]])
    audio_codec(t_np, t_s_np, False)
    ims_app.run(host='0.0.0.0', port=39005)
