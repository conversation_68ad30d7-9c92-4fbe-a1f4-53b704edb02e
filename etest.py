import requests
import time
import os
def test_asr_service(audio_file_path, call_id):
    url = "http://127.0.0.1:38004/api/v1/receive_audio"
    files = {'audio_file': open(audio_file_path, 'rb')}
    data = {'id': call_id}
    
    response = requests.post(url, files=files, data=data)
    
    print("Response Status Code:", response.status_code)
    print("Response JSON:", response.json())

if __name__ == "__main__":
    st = time.time()
    path = "/home/<USER>/clips/c1.mp3"
    callid = os.path.splitext(os.path.basename(path))[0]
    test_asr_service(path, callid)
    et = time.time()
    print("Time:", et-st)